import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { MedicineItem } from '../model/MedicineItem';
import { ItemType, getItemTypeIcon, getItemTypeName, ALL_ITEM_TYPES } from '../model/ItemType';
import { DBManager } from '../database/DBManager';

@Entry
@Component
struct AddItem {
  @State itemName: string = '';
  @State itemLocation: string = '';
  @State itemPrice: string = '0.00';
  @State expiryDays: number = 0;
  @State selectedType: ItemType = ItemType.GENERAL;
  
  // 位置选择器相关变量
  @State showLocationPicker: boolean = false;
  // 位置选择器数据结构 - 关联性结构
  private locationMap: Record<string, string[]> = {
    '客厅': ['茶几', '电视柜', '沙发', '地柜', '装饰柜', '其他'],
    '卧室': ['床头柜', '衣柜', '梳妆台', '床下', '床上', '其他'],
    '厨房': ['冰箱', '橱柜', '碗柜', '调料架', '水槽', '其他'],
    '书房': ['书架', '书桌', '抽屉', '文件柜', '其他'],
    '浴室': ['洗漱台', '浴室柜', '置物架', '其他'],
    '阳台': ['晾衣架', '储物箱', '花盆架', '其他'],
    '其他': ['其他']
  };
  
  // 第三级位置选项 - 所有区域通用
  private thirdLevelOptions: string[] = ['上层', '中层', '下层', '药盒', '药箱', '左侧', '右侧', '其他'];
  
  // 当前可选的位置选项
  @State locationLevels: string[][] = [
    Object.keys(this.locationMap), // 第一级为所有区域
    [], // 第二级初始为空，将根据选择的第一级动态填充
    this.thirdLevelOptions // 第三级为通用选项
  ];
  @State selectedLocation: string[] = ['客厅', '茶几', '上层']; // 默认选中的位置
  @State currentPickerLevel: number = 0; // 当前正在编辑的位置层级
  
  // 日期相关
  @State expiryDate: string = '';
  @State productionDate: string = '';
  @State expiryUnitIsMonth: boolean = true; // 保质期单位是否为月
  @State expiryMonths: string = '12';
  @State expiryDaysInput: string = '365';
  
  // 物品类型选择器相关变量
  @State showTypeSelector: boolean = false;
  
  // 数据库管理器实例
  private dbManager: DBManager = DBManager.getInstance();

  aboutToAppear() {
    // 初始化数据库
    this.initDatabase();
    
    // 初始化日期为当天
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    this.productionDate = `${year}/${month}/${day}`;
    
    // 初始化保质期
    this.calculateExpiryDate();
    
    // 初始化第二级位置选项
    this.updateSecondLevelOptions(this.selectedLocation[0]);
  }

  /**
   * 初始化数据库
   */
  private async initDatabase() {
    try {
      await this.dbManager.initDatabase();
      console.info('数据库初始化成功');
    } catch (error) {
      console.error('数据库初始化失败', error);
      promptAction.showToast({
        message: '数据库初始化失败',
        duration: 3000
      });
    }
  }

  /**
   * 计算保质期至日期
   */
  calculateExpiryDate() {
    try {
      // 解析生产日期
      const parts = this.productionDate.split('/');
      if (parts.length === 3) {
        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const day = parseInt(parts[2]);
        const prodDate = new Date(year, month, day);
        
        // 根据保质期单位计算保质期日期
        const expiryDate = new Date(prodDate);
        if (this.expiryUnitIsMonth) {
          const months = parseInt(this.expiryMonths) || 0;
          expiryDate.setMonth(expiryDate.getMonth() + months);
          this.expiryDays = Math.round((expiryDate.getTime() - prodDate.getTime()) / (1000 * 60 * 60 * 24));
        } else {
          const days = parseInt(this.expiryDaysInput) || 0;
          expiryDate.setDate(expiryDate.getDate() + days);
          this.expiryDays = days;
        }
        
        // 格式化保质期至日期
        const expYear = expiryDate.getFullYear();
        const expMonth = String(expiryDate.getMonth() + 1).padStart(2, '0');
        const expDay = String(expiryDate.getDate()).padStart(2, '0');
        this.expiryDate = `${expYear}/${expMonth}/${expDay}`;
      }
    } catch (error) {
      console.error('计算保质期至日期失败', error);
    }
  }

  /**
   * 更新第二级位置选项
   */
  updateSecondLevelOptions(firstLevel: string) {
    if (this.locationMap[firstLevel]) {
      this.locationLevels[1] = this.locationMap[firstLevel];
      
      // 如果当前选择的第二级不在新的选项中，重置为第一个选项
      if (!this.locationLevels[1].includes(this.selectedLocation[1])) {
        this.selectedLocation[1] = this.locationLevels[1][0];
      }
    }
  }

  /**
   * 处理位置选择
   */
  handleLocationSelection(level: number, option: string) {
    this.selectedLocation[level] = option;
    
    // 如果选择的是第一级，需要更新第二级选项
    if (level === 0) {
      this.updateSecondLevelOptions(option);
    }
    
    // 如果是第三级选择，完成选择并更新位置字符串
    if (level === 2) {
      this.itemLocation = this.selectedLocation.join('/');
      this.showLocationPicker = false;
    } else {
      // 切换到下一级选择
      this.currentPickerLevel = level + 1;
    }
  }

  /**
   * 保存物品到数据库
   */
  async saveItem() {
    // 检查必填字段
    if (this.itemName.trim() === '') {
      promptAction.showToast({
        message: '物品名称不能为空',
        duration: 3000
      });
      return;
    }
    
    if (this.itemLocation.trim() === '') {
      promptAction.showToast({
        message: '物品位置不能为空',
        duration: 3000
      });
      return;
    }
    
    // 解析价格
    const price = parseFloat(this.itemPrice);
    if (isNaN(price) || price < 0) {
      promptAction.showToast({
        message: '价格格式不正确',
        duration: 3000
      });
      return;
    }
    
    try {
      // 创建物品对象
      const newItem = new MedicineItem(
        this.itemName,
        this.itemLocation,
        $r('app.media.add'),
        this.expiryDays,
        this.selectedType,
        price
      );
      
      // 设置生产日期
      if (this.productionDate) {
        const dateParts = this.productionDate.split('/').map(Number);
        if (dateParts.length === 3) {
          const productionTimestamp = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]).getTime();
          newItem.productionDate = productionTimestamp;
          console.info('设置生产日期:', this.productionDate, '时间戳:', productionTimestamp);
        }
      }
      
      // 保存到数据库
      const id = await this.dbManager.addItem(newItem);
      console.info('物品添加成功，ID:', id);
      
      // 提示用户并返回上一页
      promptAction.showToast({
        message: '物品添加成功',
        duration: 2000
      });
      
      // 返回首页
      setTimeout(() => {
        router.back();
      }, 1000);
    } catch (error) {
      console.error('添加物品失败', error);
      promptAction.showToast({
        message: '添加物品失败',
        duration: 3000
      });
    }
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Image($r('app.media.add'))
          .width(24)
          .height(24)
          .margin({ left: 16 })
          .onClick(() => {
            router.back();
          })
        
        Text('添加物品')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
        
        Blank()
          .width(56)
      }
      .width('100%')
      .height(56)
      
      // 表单内容
      Scroll() {
        Column() {
          // 物品名称
          Column() {
            Text('物品名称')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })
            
            TextInput({ placeholder: '请输入物品名称', text: this.itemName })
              .width('100%')
              .height(48)
              .borderRadius(8)
              .backgroundColor('#f5f5f5')
              .padding({ left: 16, right: 16 })
              .onChange((value: string) => {
                this.itemName = value;
              })
          }
          .width('100%')
          .margin({ bottom: 20 })
          
          // 物品位置
          Column() {
            Text('物品位置')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })
            
            Row() {
              Text(this.itemLocation || '请选择物品位置')
                .width('100%')
                .height(48)
                .borderRadius(8)
                .backgroundColor('#f5f5f5')
                .padding({ left: 16, right: 16 })
                .fontSize(16)
                .textAlign(TextAlign.Start)
                .opacity(this.itemLocation ? 1 : 0.6)
            }
            .width('100%')
            .onClick(() => {
              this.showLocationPicker = true;
              this.currentPickerLevel = 0; // 从第一级开始选择
            })
          }
          .width('100%')
          .margin({ bottom: 20 })
          
          // 物品价格
          Column() {
            Text('物品价格')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })
            
            Row() {
              Text('¥')
                .fontSize(16)
                .margin({ left: 16 })
              
              TextInput({ placeholder: '0.00', text: this.itemPrice })
                .width('100%')
                .height(48)
                .borderRadius(8)
                .backgroundColor('#f5f5f5')
                .padding({ right: 16 })
                .type(InputType.Number)
                .onChange((value: string) => {
                  this.itemPrice = value;
                })
            }
            .width('100%')
            .borderRadius(8)
            .backgroundColor('#f5f5f5')
          }
          .width('100%')
          .margin({ bottom: 20 })
          
          // 物品类型
          Column() {
            Text('物品类型')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })
            
            Row() {
              Image(getItemTypeIcon(this.selectedType))
                .width(24)
                .height(24)
                .margin({ left: 16, right: 8 })
              
              Text(getItemTypeName(this.selectedType))
                .fontSize(16)
                .layoutWeight(1)
              
              Image($r('app.media.add'))
                .width(20)
                .height(20)
                .margin({ right: 16 })
            }
            .width('100%')
            .height(48)
            .borderRadius(8)
            .backgroundColor('#f5f5f5')
            .onClick(() => {
              this.showTypeSelector = true;
            })
          }
          .width('100%')
          .margin({ bottom: 20 })
          
          // 保质期设置
          Column() {
            Text('保质期设置')
              .fontSize(16)
              .fontWeight(FontWeight.Bold)
              .alignSelf(ItemAlign.Start)
              .margin({ bottom: 8 })
            
            // 生产日期
            Row() {
              Text('生产日期:')
                .fontSize(16)
                .width(80)
              
              Text(this.productionDate)
                .fontSize(16)
                .layoutWeight(1)
                .textAlign(TextAlign.End)
            }
            .width('100%')
            .height(48)
            .padding({ left: 16, right: 16 })
            .borderRadius(8)
            .backgroundColor('#f5f5f5')
            .margin({ bottom: 10 })
            
            // 保质期单位选择
            Row() {
              Text('保质期:')
                .fontSize(16)
                .width(80)
              
              Row() {
                TextInput({ placeholder: '12', text: this.expiryMonths })
                  .width(80)
                  .height(40)
                  .type(InputType.Number)
                  .enabled(this.expiryUnitIsMonth)
                  .fontColor(this.expiryUnitIsMonth ? '#000000' : '#999999')
                  .backgroundColor(this.expiryUnitIsMonth ? '#ffffff' : '#f0f0f0')
                  .onChange((value: string) => {
                    this.expiryMonths = value;
                    this.calculateExpiryDate();
                  })
                
                Text('个月')
                  .fontSize(16)
                  .margin({ left: 8, right: 16 })
              }
              .height(40)
              .borderRadius(20)
              .backgroundColor(this.expiryUnitIsMonth ? '#e6f7ff' : '#f5f5f5')
              .padding({ left: 10, right: 10 })
              .onClick(() => {
                this.expiryUnitIsMonth = true;
                this.calculateExpiryDate();
              })
              
              Row() {
                TextInput({ placeholder: '365', text: this.expiryDaysInput })
                  .width(80)
                  .height(40)
                  .type(InputType.Number)
                  .enabled(!this.expiryUnitIsMonth)
                  .fontColor(!this.expiryUnitIsMonth ? '#000000' : '#999999')
                  .backgroundColor(!this.expiryUnitIsMonth ? '#ffffff' : '#f0f0f0')
                  .onChange((value: string) => {
                    this.expiryDaysInput = value;
                    this.calculateExpiryDate();
                  })
                
                Text('天')
                  .fontSize(16)
                  .margin({ left: 8 })
              }
              .height(40)
              .borderRadius(20)
              .backgroundColor(!this.expiryUnitIsMonth ? '#e6f7ff' : '#f5f5f5')
              .padding({ left: 10, right: 10 })
              .onClick(() => {
                this.expiryUnitIsMonth = false;
                this.calculateExpiryDate();
              })
            }
            .width('100%')
            .padding({ left: 16, right: 16 })
            .margin({ bottom: 10 })
            .justifyContent(FlexAlign.SpaceBetween)
            
            // 保质期至日期
            Row() {
              Text('保质期至:')
                .fontSize(16)
                .width(80)
              
              Text(this.expiryDate)
                .fontSize(16)
                .layoutWeight(1)
                .textAlign(TextAlign.End)
            }
            .width('100%')
            .height(48)
            .padding({ left: 16, right: 16 })
            .borderRadius(8)
            .backgroundColor('#f5f5f5')
          }
          .width('100%')
          .margin({ bottom: 20 })
          
          // 保存按钮
          Button('保存物品')
            .width('100%')
            .height(48)
            .borderRadius(24)
            .backgroundColor('#007DFF')
            .fontSize(18)
            .fontWeight(FontWeight.Bold)
            .margin({ top: 20, bottom: 50 })
            .onClick(() => {
              this.saveItem();
            })
        }
        .width('90%')
        .padding({ top: 20, bottom: 20 })
      }
      .layoutWeight(1)

      // 位置选择器弹窗层  
      if (this.showLocationPicker) {
        Column() {
          // 标题
          Row() {
            Text('选择位置')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)
            
            Image($r('app.media.add'))
              .width(24)
              .height(24)
              .margin({ right: 16 })
              .onClick(() => {
                this.showLocationPicker = false;
              })
          }
          .width('100%')
          .height(56)
          .padding({ left: 20 })
          
          // 已选位置路径
          Row() {
            Text(this.selectedLocation.slice(0, this.currentPickerLevel).join(' > '))
              .fontSize(16)
              .opacity(0.6)
          }
          .width('100%')
          .height(40)
          .padding({ left: 20, right: 20 })
          
          // 位置选项列表
          List() {
            ForEach(this.locationLevels[this.currentPickerLevel], (option: string) => {
              ListItem() {
                Row() {
                  Text(option)
                    .fontSize(16)
                    .layoutWeight(1)
                  
                  if (option === this.selectedLocation[this.currentPickerLevel]) {
                    Image($r('app.media.add'))
                      .width(20)
                      .height(20)
                  }
                }
                .width('100%')
                .height(48)
                .padding({ left: 20, right: 20 })
                .onClick(() => {
                  this.handleLocationSelection(this.currentPickerLevel, option);
                })
              }
            })
          }
          .layoutWeight(1)
          
          // 返回按钮（仅在非第一级选择时显示）
          if (this.currentPickerLevel > 0) {
            Button('返回上一级')
              .width('90%')
              .height(48)
              .borderRadius(24)
              .backgroundColor('#e0e0e0')
              .fontSize(16)
              .margin({ bottom: 20 })
              .onClick(() => {
                this.currentPickerLevel -= 1;
              })
          }
        }
        .width('100%')
        .height('60%')
        .position({ x: 0, y: '40%' })
        .backgroundColor(Color.White)
        .borderRadius({ topLeft: 24, topRight: 24 })
      }
      
      // 类型选择器弹窗
      if (this.showTypeSelector) {
        Column() {
          // 标题
          Row() {
            Text('选择物品类型')
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .layoutWeight(1)
              .textAlign(TextAlign.Center)
            
            Image($r('app.media.add'))
              .width(24)
              .height(24)
              .margin({ right: 16 })
              .onClick(() => {
                this.showTypeSelector = false;
              })
          }
          .width('100%')
          .height(56)
          .padding({ left: 20 })
          
          // 类型选项列表
          Grid() {
            ForEach(ALL_ITEM_TYPES, (itemType: ItemType) => {
              GridItem() {
                Column() {
                  Image(getItemTypeIcon(itemType))
                    .width(48)
                    .height(48)
                    .border({
                      width: itemType === this.selectedType ? 2 : 0,
                      color: '#007DFF',
                      radius: 24
                    })
                    .padding(8)
                  
                  Text(getItemTypeName(itemType))
                    .fontSize(14)
                    .margin({ top: 8 })
                    .fontColor(itemType === this.selectedType ? '#007DFF' : '#000000')
                }
                .width('100%')
                .height(90)
                .onClick(() => {
                  this.selectedType = itemType;
                  this.showTypeSelector = false;
                })
              }
            })
          }
          .columnsTemplate('1fr 1fr 1fr 1fr')
          .rowsGap(16)
          .columnsGap(16)
          .padding({ left: 20, right: 20, top: 10, bottom: 30 })
        }
        .width('100%')
        .position({ x: 0, y: '40%' })
        .height('60%')
        .backgroundColor(Color.White)
        .borderRadius({ topLeft: 24, topRight: 24 })
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
} 