import router from '@ohos.router';
import { ThemeProvider } from '../common/components/ThemeProvider';

@Entry
@Component
struct Index {
  @State message: string = 'Hello World';

  aboutToAppear() {
    // 模拟登录成功后自动跳转到Home页面
    setTimeout(() => {
      router.pushUrl({ url: 'pages/Home' });
    }, 100);
  }

  build() {
    Row() {
      // 使用主题提供者包装整个应用
      ThemeProvider() {
        Column() {
          Text('正在跳转到首页...')
            .fontSize(16)
            .fontColor('#666666')
        }
        .width('100%')
        .height('100%')
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
  }
}