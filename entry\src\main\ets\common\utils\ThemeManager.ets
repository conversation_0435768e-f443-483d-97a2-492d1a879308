import preferences from '@ohos.data.preferences';
import window from '@ohos.window';
import { ThemeConstants, ThemeColors } from '../constants/ThemeConstants';

// Define the WindowProperties interface to include isDarkMode
interface MyWindowProperties {
  isDarkMode?: boolean;
}

/**
 * 主题管理器
 * 负责存储和获取应用主题设置，并监听系统暗黑模式变化
 */
export class ThemeManager {
  private static readonly PREFERENCES_NAME: string = 'app_preferences';
  private static readonly THEME_KEY: string = 'app_theme';
  private static instance: ThemeManager;
  private preferences: preferences.Preferences | null = null;
  private systemDarkMode: boolean = false;
  private themeChangeCallbacks: ((isDark: boolean) => void)[] = [];
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }
  
  /**
   * 初始化首选项和系统暗色模式监听
   */
  public async init(): Promise<void> {
    try {
      // 初始化首选项
      this.preferences = await preferences.getPreferences(getContext(this), ThemeManager.PREFERENCES_NAME);
      console.info('主题管理器初始化成功');
      
      // 获取系统当前暗黑模式状态
      await this.checkSystemDarkMode();
      
      // 监听系统暗黑模式变化
      this.listenToSystemDarkModeChanges();
    } catch (err) {
      console.error('主题管理器初始化失败', err);
    }
  }
  
  /**
   * 检查系统当前暗黑模式状态
   */
  private async checkSystemDarkMode(): Promise<void> {
    try {
      const windowClass = await window.getLastWindow(getContext(this));
      const properties = windowClass.getWindowProperties() as MyWindowProperties;
      this.systemDarkMode = properties.isDarkMode || false;
      console.info('系统暗黑模式状态:', this.systemDarkMode);
    } catch (err) {
      console.error('获取系统暗黑模式状态失败', err);
      this.systemDarkMode = false;
    }
  }
  
  /**
   * 监听系统暗黑模式变化
   */
  private async listenToSystemDarkModeChanges(): Promise<void> {
    try {
      // 在当前版本的HarmonyOS中，我们采用不同方式检测系统主题变化
      // 由于不直接支持isDarkMode变化监听，我们定期检查
      setInterval(async () => {
        try {
          const prevDarkMode = this.systemDarkMode;
          await this.checkSystemDarkMode();
          
          // 如果暗黑模式状态变化且当前设置为跟随系统，则通知主题变化
          if (prevDarkMode !== this.systemDarkMode) {
            const theme = await this.getTheme();
            if (theme === ThemeConstants.THEME_AUTO) {
              this.notifyThemeChange(this.systemDarkMode);
            }
          }
        } catch (error) {
          console.error('检查暗黑模式状态失败', error);
        }
      }, 2000); // 每2秒检查一次
    } catch (err) {
      console.error('设置系统暗黑模式监听失败', err);
    }
  }
  
  /**
   * 获取当前主题
   * @returns 主题名称，默认跟随系统
   */
  public async getTheme(): Promise<string> {
    if (!this.preferences) {
      await this.init();
    }
    
    try {
      const theme = await this.preferences?.get(ThemeManager.THEME_KEY, ThemeConstants.THEME_AUTO) as string;
      return theme;
    } catch (err) {
      console.error('获取主题失败', err);
      return ThemeConstants.THEME_AUTO;
    }
  }
  
  /**
   * 设置主题
   * @param theme 主题名称
   */
  public async setTheme(theme: string): Promise<void> {
    if (!this.preferences) {
      await this.init();
    }
    
    try {
      await this.preferences?.put(ThemeManager.THEME_KEY, theme);
      await this.preferences?.flush();
      console.info('主题设置成功', theme);
      
      // 通知主题变化
      const isDarkMode = await this.isDarkMode();
      this.notifyThemeChange(isDarkMode);
    } catch (err) {
      console.error('主题设置失败', err);
    }
  }
  
  /**
   * 判断当前是否为深色模式
   */
  public async isDarkMode(): Promise<boolean> {
    const theme = await this.getTheme();
    
    if (theme === ThemeConstants.THEME_DARK) {
      return true;
    } else if (theme === ThemeConstants.THEME_LIGHT) {
      return false;
    } else {
      // 跟随系统
      return this.systemDarkMode;
    }
  }
  
  /**
   * 获取主题颜色
   * @returns 主题颜色配置
   */
  public async getThemeColors(): Promise<ThemeColors> {
    const isDark = await this.isDarkMode();
    return isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
  }
  
  /**
   * 注册主题变化回调
   * @param callback 回调函数
   */
  public registerThemeChangeCallback(callback: (isDark: boolean) => void): void {
    this.themeChangeCallbacks.push(callback);
  }
  
  /**
   * 移除主题变化回调
   * @param callback 回调函数
   */
  public unregisterThemeChangeCallback(callback: (isDark: boolean) => void): void {
    const index = this.themeChangeCallbacks.indexOf(callback);
    if (index !== -1) {
      this.themeChangeCallbacks.splice(index, 1);
    }
  }
  
  /**
   * 通知主题变化
   * @param isDark 是否为深色模式
   */
  private notifyThemeChange(isDark: boolean): void {
    this.themeChangeCallbacks.forEach(callback => {
      callback(isDark);
    });
  }
} 