import { speechRecognizer } from '@kit.CoreSpeechKit';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 华为官方语音识别器
 * 使用华为Core Speech Kit API实现本地语音识别
 */
export class HuaweiSpeechRecognizer {
  private static instance: HuaweiSpeechRecognizer;
  private speechRecognitionEngine: speechRecognizer.SpeechRecognitionEngine | null = null;
  private currentSessionId: string = '';
  private isRecognizing: boolean = false;
  
  // 回调函数
  public onRecognitionResult?: (text: string) => void;
  public onRecognitionError?: (error: string) => void;
  public onRecognitionStart?: () => void;
  public onRecognitionComplete?: () => void;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): HuaweiSpeechRecognizer {
    if (!HuaweiSpeechRecognizer.instance) {
      HuaweiSpeechRecognizer.instance = new HuaweiSpeechRecognizer();
    }
    return HuaweiSpeechRecognizer.instance;
  }

  /**
   * 初始化语音识别引擎
   */
  public async initSpeechEngine(): Promise<void> {
    try {
      if (this.speechRecognitionEngine) {
        console.info('语音识别引擎已初始化');
        return;
      }

      // 创建引擎参数 - 根据华为官方文档配置
      const extraParam: Record<string, Object> = {
        "locate": "CN", // 区域信息，当前仅支持"CN"
        "recognizerMode": "short" // 识别模式：short(短语音) 或 long(长语音)
      };

      const createEngineParams: speechRecognizer.CreateEngineParams = {
        language: 'zh-CN', // 中文识别
        online: 1, // 1表示离线模式，目前只支持离线引擎
        extraParams: extraParam // 额外参数
      };

      // 创建语音识别引擎
      this.speechRecognitionEngine = await speechRecognizer.createEngine(createEngineParams);
      
      if (this.speechRecognitionEngine) {
        console.info('华为语音识别引擎初始化成功');
        this.setupRecognitionListener();
      } else {
        throw new Error('语音识别引擎创建失败');
      }
    } catch (error) {
      console.error('初始化语音识别引擎失败:', error);
      throw new Error('初始化语音识别引擎失败: ' + (error as BusinessError).message);
    }
  }

  /**
   * 设置识别监听器
   */
  private setupRecognitionListener(): void {
    if (!this.speechRecognitionEngine) {
      return;
    }

    const recognitionListener: speechRecognizer.RecognitionListener = {
      onStart: (sessionId: string, eventMessage: string) => {
        console.info('语音识别开始:', sessionId, eventMessage);
        this.isRecognizing = true;
        this.onRecognitionStart?.();
      },

      onEvent: (sessionId: string, eventCode: number, eventMessage: string) => {
        console.info('语音识别事件:', sessionId, eventCode, eventMessage);
      },

      onResult: (sessionId: string, result: speechRecognizer.SpeechRecognitionResult) => {
        console.info('语音识别结果:', sessionId, result);
        if (result && result.result && result.result.length > 0) {
          const recognizedText = Array.isArray(result.result) ? result.result.join('') : result.result.toString();
          console.info('识别文本:', recognizedText);
          this.onRecognitionResult?.(recognizedText);
        }
      },

      onComplete: (sessionId: string, eventMessage: string) => {
        console.info('语音识别完成:', sessionId, eventMessage);
        this.isRecognizing = false;
        this.onRecognitionComplete?.();
      },

      onError: (sessionId: string, errorCode: number, errorMessage: string) => {
        console.error('语音识别错误:', sessionId, errorCode, errorMessage);
        this.isRecognizing = false;
        this.onRecognitionError?.(`识别错误: ${errorMessage} (${errorCode})`);
      }
    };

    // 设置回调
    this.speechRecognitionEngine.setListener(recognitionListener);
  }

  /**
   * 开始语音识别
   */
  public async startRecognition(): Promise<void> {
    try {
      if (!this.speechRecognitionEngine) {
        await this.initSpeechEngine();
      }

      if (this.isRecognizing) {
        console.warn('语音识别已在进行中');
        return;
      }

      // 生成会话ID
      this.currentSessionId = 'session_' + Date.now();

      // 配置识别参数 - 根据华为官方文档配置
      const audioParam: speechRecognizer.AudioInfo = {
        audioType: 'pcm', // 目前只支持PCM
        sampleRate: 16000, // 当前仅支持16000采样率
        soundChannel: 1, // 当前仅支持通道1
        sampleBit: 16 // 当前仅支持16位
      };

      const extraParam: Record<string, Object> = {
        "recognitionMode": 0, // 0：实时录音识别（需要录音权限）
        "vadBegin": 2000, // VAD前端点设置，范围[500,10000]ms，默认10000ms
        "vadEnd": 3000, // VAD后端点设置，范围[500,10000]ms，默认800ms
        "maxAudioDuration": 40000 // 最大音频时长，短语音模式[20000-60000]ms
      };

      const startParams: speechRecognizer.StartParams = {
        sessionId: this.currentSessionId,
        audioInfo: audioParam,
        extraParams: extraParam
      };

      // 开始识别
      await this.speechRecognitionEngine!.startListening(startParams);
      console.info('语音识别启动成功');

    } catch (error) {
      console.error('开始语音识别失败:', error);
      this.isRecognizing = false;
      throw new Error('开始识别失败');
    }
  }

  /**
   * 停止语音识别
   */
  public async stopRecognition(): Promise<void> {
    try {
      if (!this.speechRecognitionEngine || !this.currentSessionId) {
        console.warn('语音识别引擎未初始化或无活动会话');
        return;
      }

      if (!this.isRecognizing) {
        console.warn('语音识别未在进行中');
        return;
      }

      console.info('正在停止语音识别，会话ID:', this.currentSessionId);

      // 停止识别 - 使用finish方法正常结束识别
      await this.speechRecognitionEngine.finish(this.currentSessionId);
      console.info('语音识别停止成功');

      // 更新状态
      this.isRecognizing = false;

    } catch (error) {
      console.error('停止语音识别失败:', error);
      // 即使停止失败，也要重置状态
      this.isRecognizing = false;
      throw new Error('停止识别失败: ' + (error as Error).message);
    }
  }

  /**
   * 取消语音识别
   */
  public async cancelRecognition(): Promise<void> {
    try {
      if (!this.speechRecognitionEngine || !this.currentSessionId) {
        return;
      }

      if (this.isRecognizing) {
        await this.speechRecognitionEngine.cancel(this.currentSessionId);
        this.isRecognizing = false;
        console.info('语音识别取消成功');
      }

    } catch (error) {
      console.error('取消语音识别失败:', error);
      throw new Error('取消识别失败');
    }
  }

  /**
   * 释放资源
   */
  public async release(): Promise<void> {
    try {
      if (this.isRecognizing) {
        await this.cancelRecognition();
      }

      if (this.speechRecognitionEngine) {
        await this.speechRecognitionEngine.shutdown();
        this.speechRecognitionEngine = null;
        console.info('华为语音识别引擎资源释放成功');
      }

    } catch (error) {
      console.error('释放华为语音识别引擎资源失败:', error);
      throw new Error('释放资源失败');
    }
  }

  /**
   * 检查是否正在识别
   */
  public isRecognitionActive(): boolean {
    return this.isRecognizing;
  }

  /**
   * 获取支持的语言列表
   */
  public async getSupportedLanguages(): Promise<string[]> {
    try {
      if (this.speechRecognitionEngine) {
        // 根据华为官方API，listLanguages需要传入查询参数
        const languageQuery: speechRecognizer.LanguageQuery = {
          sessionId: 'language_query_' + Date.now()
        };
        const languages = await this.speechRecognitionEngine.listLanguages(languageQuery);
        return Array.isArray(languages) ? languages : ['zh-CN'];
      }
      return ['zh-CN']; // 默认支持中文
    } catch (error) {
      console.error('查询支持语言失败:', error);
      return ['zh-CN'];
    }
  }
}
