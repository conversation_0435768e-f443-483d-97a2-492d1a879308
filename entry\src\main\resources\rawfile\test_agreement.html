<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试协议页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #0972d3;
            font-size: 24px;
        }
        h2 {
            color: #333;
            font-size: 20px;
            margin-top: 20px;
        }
        p {
            margin: 10px 0;
            color: #444;
        }
        .highlight {
            background-color: #f0f7ff;
            padding: 10px;
            border-left: 4px solid #0972d3;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>测试协议页面</h1>
    
    <div class="highlight">
        <p>这是一个本地测试页面，用于验证WebView组件的功能是否正常。</p>
    </div>
    
    <h2>协议内容</h2>
    <p>这里是协议的测试内容，包含了一些基本的HTML元素和样式。</p>
    <p>如果您能看到这个页面，说明WebView组件已经能够正确加载本地HTML文件。</p>
    
    <h2>JavaScript测试</h2>
    <p>点击下面的按钮测试JavaScript功能：</p>
    <button onclick="showAlert()">测试JavaScript</button>
    
    <h2>链接测试</h2>
    <p>测试链接跳转：<a href="https://www.example.com" target="_blank">Example.com</a></p>
    
    <script>
        function showAlert() {
            alert('JavaScript功能正常！');
        }
        
        // 页面加载完成后执行
        window.onload = function() {
            console.log('测试页面加载完成');
        };
    </script>
</body>
</html>