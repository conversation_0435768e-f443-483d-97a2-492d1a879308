/**
 * Simple logging utility class
 */
export default class Logger {
  /**
   * Log info message
   * @param tag The tag for the log message
   * @param message The message to log
   */
  static info(tag: string, message: string): void {
    console.info(`[${tag}] ${message}`);
  }

  /**
   * Log error message
   * @param tag The tag for the log message
   * @param message The message to log
   */
  static error(tag: string, message: string): void {
    console.error(`[${tag}] ${message}`);
  }

  /**
   * Log warning message
   * @param tag The tag for the log message
   * @param message The message to log
   */
  static warn(tag: string, message: string): void {
    console.warn(`[${tag}] ${message}`);
  }

  /**
   * Log debug message
   * @param tag The tag for the log message
   * @param message The message to log
   */
  static debug(tag: string, message: string): void {
    console.debug(`[${tag}] ${message}`);
  }
} 