import { router } from '@kit.ArkUI';
import promptAction from '@ohos.promptAction';
import { UIContext } from '@ohos.arkui.UIContext';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';
import { NotificationManager, NotificationItem, NotificationType } from '../common/utils/NotificationManager';

// 自定义路由参数接口
interface RouterParams {
  unreadNotifications?: number;
}

// 对话框按钮类型
class DialogButton {
  text: string;
  color: string;
  
  constructor(text: string, color: string) {
    this.text = text;
    this.color = color;
  }
}

/**
 * 消息通知页面
 */
@Entry
@Component
struct Notifications {
  // 使用 promptAction 替代弃用的方法
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  @State notificationList: NotificationItem[] = [];
  @State unreadCount: number = 0;
  // 添加状态变量，记录当前滑动的通知项ID，-1表示没有滑动项
  @State swipedItemId: number = -1;
  // 添加状态变量，跟踪每个通知项的滑动偏移量
  @State swipeOffsets: Map<number, number> = new Map<number, number>();
  
  // 添加滚动控制器
  scroller: Scroller = new Scroller();
  
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  // 通知管理器
  private notificationManager: NotificationManager = NotificationManager.getInstance();

  aboutToAppear() {
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      // 加载当前主题设置
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
    
    // 加载通知数据
    this.loadNotifications();
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
    
    // 计算最新的未读数量并同步到通知管理器，但不执行导航操作
    this.calculateUnreadCount();
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      this.isDarkMode = await this.themeManager.isDarkMode();
      
      // 更新主题颜色
      this.colors = this.isDarkMode ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
      
      console.info('加载主题设置', '是否暗黑模式:', this.isDarkMode);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('Notifications页面主题更新', isDark ? '深色模式' : '浅色模式');
  }
  
  /**
   * 加载通知数据
   */
  private loadNotifications() {
    // 从通知管理器获取通知数据
    this.notificationList = this.notificationManager.getNotifications();
    
    // 更新未读数量
    this.calculateUnreadCount();
  }
  
  /**
   * 计算未读消息数量
   */
  private calculateUnreadCount() {
    this.unreadCount = this.notificationList.filter(item => !item.isRead).length;
    
    // 同步到通知管理器
    this.notificationManager.setUnreadCount(this.unreadCount);
  }
  
  /**
   * 更新未读数量并导航回首页
   */
  private updateUnreadCount() {
    // 计算最新的未读数量
    this.calculateUnreadCount();
    
    // 直接使用router.back()返回上一页，使用我们定义的转场动画
    router.back();
  }
  
  /**
   * 标记通知为已读
   */
  markAsRead(id: number) {
    // 使用通知管理器标记通知为已读
    this.notificationManager.markAsRead(id);
    
    // 重新加载通知列表以更新UI
    this.loadNotifications();
    
    // 重置滑动状态
    this.swipedItemId = -1;
    
    promptAction.showToast({ message: '已标记为已读' });
  }
  
  /**
   * 标记通知为未读
   * @param id 通知ID
   */
  markAsUnread(id: number) {
    // 使用通知管理器标记通知为未读
    this.notificationManager.markAsUnread(id);
    
    // 重新加载通知列表以更新UI
    this.loadNotifications();
    
    // 重置滑动状态
    this.swipedItemId = -1;
    
    promptAction.showToast({ message: '已标记为未读' });
  }
  
  /**
   * 标记所有通知为已读
   */
  markAllAsRead() {
    if (this.unreadCount === 0) {
      promptAction.showToast({ message: '没有未读通知' });
      return;
    }
    
    // 使用通知管理器标记所有通知为已读
    this.notificationManager.markAllAsRead();
    
    // 重新加载通知列表以更新UI
    this.loadNotifications();
    
    // 重置滑动状态
    this.swipedItemId = -1;
    
    promptAction.showToast({ message: '已将所有通知标记为已读' });
  }
  
  /**
   * 清空所有通知
   */
  clearAllNotifications() {
    if (this.notificationList.length === 0) {
      promptAction.showToast({ message: '没有可清空的通知' });
      return;
    }
    
    const buttons: DialogButton[] = [
      new DialogButton('取消', this.isDarkMode ? '#AAAAAA' : '#666666'),
      new DialogButton('确定', '#E53935')
    ];
    
    // 使用 promptAction 替代弃用的方法
    promptAction.showDialog({
      title: '清空通知',
      message: '确定要清空所有通知吗？',
      buttons: buttons
    })
    .then(result => {
      if (result.index === 1) {
        // 使用通知管理器清空所有通知
        this.notificationManager.clearAllNotifications();
        
        // 重新加载通知列表以更新UI
        this.loadNotifications();
        
        promptAction.showToast({ message: '已清空所有通知' });
      }
    })
    .catch((err: Error) => {
      console.error('对话框显示失败', err);
    });
  }

  /**
   * 打开通知详情页面
   * @param id 通知ID
   */
  private openNotificationDetail(id: number) {
    // 标记通知为已读
    if (!this.notificationList.find(item => item.id === id)?.isRead) {
      this.markAsRead(id);
    }
    
    // 跳转到通知详情页面
    router.pushUrl({
      url: 'pages/NotificationDetail',
      params: {
        notificationId: id
      }
    });
  }
  
  /**
   * 删除通知
   * @param id 通知ID
   */
  private deleteNotification(id: number) {
    const buttons: DialogButton[] = [
      new DialogButton('取消', this.isDarkMode ? '#AAAAAA' : '#666666'),
      new DialogButton('确定', '#E53935')
    ];
    
    promptAction.showDialog({
      title: '删除通知',
      message: '确定要删除这条通知吗？',
      buttons: buttons
    })
    .then(result => {
      if (result.index === 1) {
        // 删除通知
        const success = this.notificationManager.deleteNotification(id);
        if (success) {
          // 重新加载通知列表
          this.loadNotifications();
          
          // 重置滑动状态
          this.swipedItemId = -1;
          
          promptAction.showToast({ message: '通知已删除' });
        } else {
          promptAction.showToast({ message: '删除通知失败' });
        }
      }
    })
    .catch((err: Error) => {
      console.error('对话框显示失败', err);
    });
  }
  
  /**
   * 设置当前滑动的通知项
   * @param id 通知ID，传-1表示关闭所有滑动项
   */
  private setSwipedItem(id: number) {
    // 如果状态没有变化，不进行更新以避免不必要的重绘
    if (this.swipedItemId === id) {
      return;
    }
    this.swipedItemId = id;
    
    // 重置所有偏移量（除了当前滑动项）
    this.notificationList.forEach(item => {
      if (item.id !== id) {
        this.swipeOffsets.set(item.id, 0);
      }
    });
    
    // 如果关闭所有滑动项，也重置当前滑动项的偏移量
    if (id === -1) {
      this.notificationList.forEach(item => {
        this.swipeOffsets.set(item.id, 0);
      });
    }
  }
  
  /**
   * 设置通知项的滑动偏移量
   * @param id 通知ID
   * @param offset 偏移量
   */
  private setSwipeOffset(id: number, offset: number) {
    this.swipeOffsets.set(id, offset);
  }
  
  /**
   * 获取通知项的滑动偏移量
   * @param id 通知ID
   * @returns 偏移量，默认为0
   */
  private getSwipeOffset(id: number): number {
    return this.swipeOffsets.get(id) || 0;
  }
  
  /**
   * 检查通知项是否处于滑动状态
   * @param id 通知ID
   * @returns 是否处于滑动状态
   */
  private isItemSwiped(id: number): boolean {
    return this.swipedItemId === id;
  }

  /**
   * 调试辅助方法：显示手势事件信息
   * @param event 手势事件
   */
  private logGestureEvent(event: GestureEvent) {
    // 减少日志输出，仅在开发调试时使用
    if (false) {
      console.info('手势事件信息:');
      console.info('  offsetX: ' + event.offsetX);
      console.info('  offsetY: ' + event.offsetY);
      if (event.angle !== undefined) {
        console.info('  angle: ' + event.angle);
      }
      if (event.speed !== undefined) {
        console.info('  speed: ' + event.speed);
      }
    }
  }
  
  /**
   * 处理页面点击事件，关闭所有滑动项
   */
  private handlePageClick() {
    if (this.swipedItemId !== -1) {
      this.setSwipedItem(-1);
    }
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.back'))
          .width(24)
          .height(24)
          .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
          .onClick(() => {
            // 返回时更新未读数量
            this.updateUnreadCount();
          })
        Text('消息通知')
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .fontColor(this.colors.PRIMARY_TEXT)
          .margin({ left: 8 })
          
        Blank()
        
        if (this.unreadCount > 0) {
          Button('全部已读')
            .fontSize(14)
            .fontColor(this.colors.PRIMARY_COLOR)
            .backgroundColor(Color.Transparent)
            .margin({ right: 12 })
            .onClick(() => this.markAllAsRead())
        }
        
        Button('清空')
          .fontSize(14)
          .fontColor(this.colors.PRIMARY_COLOR)
          .backgroundColor(Color.Transparent)
          .onClick(() => this.clearAllNotifications())
      }
      .width('100%')
      .height(50)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(this.colors.CARD_BACKGROUND)

      // 通知列表
      if (this.notificationList.length > 0) {
        List({ scroller: this.scroller }) {
          ForEach(this.notificationList, (item: NotificationItem) => {
            ListItem() {
              // 使用Stack组件实现滑动效果
              Stack({ alignContent: Alignment.Center }) {
                // 底层放操作按钮
                Row() {
                  Blank()
                  
                  // 根据通知已读状态显示不同的操作按钮
                  if (!item.isRead) {
                    // 未读通知显示"标记已读"按钮
                    Button('标记已读')
                      .width(100)
                      .height(40)
                      .fontSize(14)
                      .fontWeight(FontWeight.Medium)
                      .fontColor(Color.White)
                      .backgroundColor('#1890FF')
                      .borderRadius(4)
                      .margin({ right: 8 })
                      .onClick((event) => {
                        this.markAsRead(item.id);
                      })
                      .stateEffect(true) // 添加点击状态效果
                  } else {
                    // 已读通知显示"标记未读"按钮
                    Button('标记未读')
                      .width(100)
                      .height(40)
                      .fontSize(14)
                      .fontWeight(FontWeight.Medium)
                      .fontColor(Color.White)
                      .backgroundColor('#1890FF')
                      .borderRadius(4)
                      .margin({ right: 8 })
                      .onClick((event) => {
                        this.markAsUnread(item.id);
                      })
                      .stateEffect(true) // 添加点击状态效果
                  }
                  
                  // 删除按钮
                  Button('删除')
                    .width(70)
                    .height(40)
                    .fontSize(14)
                    .fontWeight(FontWeight.Medium)
                    .fontColor(Color.White)
                    .backgroundColor('#E53935')
                    .borderRadius(4)
                    .onClick((event) => {
                      this.deleteNotification(item.id);
                    })
                    .stateEffect(true) // 添加点击状态效果
                }
                .width('100%')
                .justifyContent(FlexAlign.End)
                .padding({ right: 16 })
                
                // 顶层放通知内容
                Column() {
                  // 主内容
                  Column() {
                    Row() {
                      // 未读标记
                      if (!item.isRead) {
                        Circle({ width: 8, height: 8 })
                          .fill('#E53935')
                          .margin({ right: 8 })
                      } else {
                        Blank()
                          .width(16)
                      }
                      
                      Text(item.title)
                        .fontSize(16)
                        .fontWeight(FontWeight.Medium)
                        .fontColor(this.colors.PRIMARY_TEXT)
                      
                      Blank()
                      
                      // 通知类型标签 - 增强视觉效果
                      if (item.type === NotificationType.EXPIRY) {
                        Text('过期提醒')
                          .fontSize(10)
                          .fontWeight(FontWeight.Bold)  // 加粗显示
                          .fontColor('#FFFFFF')
                          .backgroundColor('#FF4D4F')
                          .borderRadius(10)
                          .padding({ left: 6, right: 6, top: 2, bottom: 2 })  // 增加垂直内边距
                          .margin({ right: 8 })
                      } else if (item.type === NotificationType.UPDATE) {
                        Text('功能更新')
                          .fontSize(10)
                          .fontWeight(FontWeight.Bold)  // 加粗显示
                          .fontColor('#FFFFFF')
                          .backgroundColor('#1890FF')
                          .borderRadius(10)
                          .padding({ left: 6, right: 6, top: 2, bottom: 2 })  // 增加垂直内边距
                          .margin({ right: 8 })
                      } else {
                        // 为系统通知也添加标签，确保所有通知类型都有标记
                        Text('系统通知')
                          .fontSize(10)
                          .fontWeight(FontWeight.Bold)  // 加粗显示
                          .fontColor('#FFFFFF')
                          .backgroundColor('#52C41A')  // 绿色背景
                          .borderRadius(10)
                          .padding({ left: 6, right: 6, top: 2, bottom: 2 })
                          .margin({ right: 8 })
                      }
                      
                      Text(item.time)
                        .fontSize(12)
                        .fontColor(this.colors.HINT_TEXT)
                    }
                    .width('100%')
                    .padding({ top: 12, bottom: 8 })
                    
                    Text(item.content)
                      .fontSize(14)
                      .fontColor(this.colors.SECONDARY_TEXT)
                      .width('100%')
                      .margin({ top: 4, bottom: 12 })
                  }
                  .width('100%')
                  .backgroundColor(
                    (item.isRead ? this.colors.CARD_BACKGROUND : this.colors.ACTIVE_ITEM_BG)
                  )
                  .borderRadius(8)
                  .padding({ left: 16, right: 16 })
                  .height('auto')
                }
                .width('100%')
                .backgroundColor(
                  (item.isRead ? this.colors.CARD_BACKGROUND : this.colors.ACTIVE_ITEM_BG)
                )
                .borderRadius(8)
                // 根据滑动偏移量设置位置
                .translate({ x: this.getSwipeOffset(item.id) })
                // 添加动画效果
                .animation({
                  duration: 200,  // 减少动画时长，提高响应速度
                  curve: Curve.FastOutSlowIn,
                  iterations: 1,
                  playMode: PlayMode.Normal
                })
                // 添加手势识别
                .gesture(
                  PanGesture({ direction: PanDirection.Horizontal, distance: 5 })
                    .onActionStart((event: GestureEvent) => {
                      // 记录手势开始时的初始偏移量
                      // 不输出日志，减轻性能负担
                    })
                    .onActionUpdate((event: GestureEvent) => {
                      // 实时更新偏移量，实现跟手效果
                      // 限制最大偏移量，防止滑动过度
                      const maxOffset = -220; // 操作按钮总宽度加上间距
                      let offset = Math.max(maxOffset, event.offsetX);
                      
                      // 限制只能向左滑动（负值）
                      offset = Math.min(0, offset);
                      
                      // 更新偏移量
                      this.setSwipeOffset(item.id, offset);
                    })
                    .onActionEnd((event: GestureEvent) => {
                      // 手势结束时的处理
                      // 不输出日志，减轻性能负担
                      
                      // 获取当前偏移量
                      const currentOffset = this.getSwipeOffset(item.id);
                      
                      // 如果向左滑动超过阈值，则显示操作按钮
                      if (currentOffset < -50) {
                        // 如果当前已有其他滑动项，先关闭它
                        if (this.swipedItemId !== -1 && this.swipedItemId !== item.id) {
                          this.setSwipedItem(-1);
                        }
                        
                        // 设置当前滑动项
                        this.setSwipedItem(item.id);
                        
                        // 设置为完全打开状态
                        this.setSwipeOffset(item.id, -220);
                      } else {
                        // 滑动距离不够，恢复原状态
                        this.setSwipeOffset(item.id, 0);
                        
                        // 如果当前项是滑动项，重置状态
                        if (this.swipedItemId === item.id) {
                          this.setSwipedItem(-1);
                        }
                      }
                    })
                )
                .onClick((event) => {
                  if (this.isItemSwiped(item.id)) {
                    // 如果已经处于滑动状态，点击内容区域关闭滑动
                    this.setSwipedItem(-1);
                  } else {
                    // 否则打开通知详情
                    this.openNotificationDetail(item.id);
                  }
                })
              }
              .width('100%')
              .height('auto')
            }
            .margin({ bottom: 8 })
            // 添加key属性，帮助框架优化列表渲染性能
            .key('notification-' + item.id)
          })
        }
        .width('100%')
        .padding(16)
        .layoutWeight(1)
        .edgeEffect(EdgeEffect.None)
        // 优化滚动性能
        .cachedCount(5)
        // 点击列表空白区域关闭所有滑动项
        .onClick(() => {
          this.handlePageClick();
        })
      } else {
        // 没有通知时显示空状态
        Column() {
          Image($r('app.media.notification_bell'))
            .width(80)
            .height(80)
            .fillColor(this.colors.HINT_TEXT)
            .opacity(0.5)
            .margin({ bottom: 16 })
          
          Text('暂无消息通知')
            .fontSize(16)
            .fontColor(this.colors.HINT_TEXT)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
    // 点击页面背景关闭所有滑动项
    .onClick(() => {
      this.handlePageClick();
    })
  }

  /**
   * 页面转场动画
   */
  pageTransition() {
    // Push操作时页面进入的动画：从右侧滑入
    PageTransitionEnter({ type: RouteType.Push, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Right)
    
    // Pop操作时页面进入的动画：从左侧滑入
    PageTransitionEnter({ type: RouteType.Pop, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Left)
    
    // Push操作时页面退出的动画：向左侧滑出
    PageTransitionExit({ type: RouteType.Push, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Left)
    
    // Pop操作时页面退出的动画：向右侧滑出
    PageTransitionExit({ type: RouteType.Pop, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Right)
  }
}