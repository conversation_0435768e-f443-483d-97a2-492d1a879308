# 华为账号登录接口文档

## 接口规范说明

本文档定义了华为账号登录功能相关的API接口规范。所有接口均遵循统一的请求响应格式。

### 统一响应格式

**成功响应**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

**错误响应**
```json
{
  "code": 错误码,
  "message": "错误描述",
  "data": null
}
```

### 认证方式

需要登录的接口使用Bearer Token认证：
```
Authorization: Bearer {token}
```

---

## 1. 华为账号登录接口

- **接口核心功能描述**：用户通过华为账号服务进行身份认证登录，无需输入用户名和密码。系统验证华为授权令牌，自动创建或绑定用户账号，返回JWT访问令牌和用户信息。支持首次登录自动注册功能。
- **接口地址**: `/api/v1/auth/huawei-login`
- **方法**: POST
- **需要登录**: 否
- **请求参数**: 
```json
{
  "huaweiAccountId": "string",     // 华为账号ID（UnionID）
  "displayName": "string",         // 显示名称
  "email": "string",               // 邮箱地址（可选）
  "phone": "string",               // 手机号码（可选）
  "authToken": "string",           // 华为授权令牌
  "deviceInfo": {                  // 设备信息
    "deviceId": "string",          // 设备ID
    "deviceModel": "string",       // 设备型号
    "osVersion": "string",         // 系统版本
    "appVersion": "string"         // 应用版本
  }
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号登录成功",
  "data": {
    "token": "string",             // JWT访问令牌
    "refreshToken": "string",      // 刷新令牌
    "expiresIn": 7200,            // 过期时间（秒）
    "user": {
      "userId": "string",          // 用户ID
      "username": "string",        // 用户名
      "displayName": "string",     // 显示名称
      "email": "string",           // 邮箱
      "phone": "string",           // 手机号
      "avatar": "string",          // 头像URL
      "isFirstLogin": true,        // 是否首次登录
      "createdAt": "2024-01-01T00:00:00Z",       // 创建时间
      "lastLoginAt": "2024-01-01T00:00:00Z"      // 最后登录时间
    },
    "huaweiAccount": {
      "accountId": "string",       // 华为账号ID
      "bindTime": "2024-01-01T00:00:00Z"         // 绑定时间
    }
  }
}
```

---

## 2. 华为账号绑定接口

- **接口核心功能描述**：将已登录用户的账号与华为账号进行绑定关联。验证华为授权令牌有效性，检查华为账号是否已被其他用户绑定，确保一个华为账号只能绑定一个应用账号。绑定成功后用户可使用华为账号快速登录。
- **接口地址**: `/api/v1/auth/bind-huawei-account`
- **方法**: POST
- **需要登录**: 是
- **请求参数**: 
```json
{
  "huaweiAccountId": "string",     // 华为账号ID
  "authToken": "string",           // 华为授权令牌
  "displayName": "string",         // 显示名称
  "email": "string"                // 邮箱地址
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号绑定成功",
  "data": {
    "bindTime": "2024-01-01T00:00:00Z",          // 绑定时间
    "huaweiAccountId": "string"    // 华为账号ID
  }
}
```

---

## 3. 华为账号解绑接口

- **接口核心功能描述**：解除当前用户账号与华为账号的绑定关系。需要用户输入密码进行身份确认，确保操作安全性。解绑后用户将无法使用华为账号登录，需使用其他登录方式。解绑操作不可逆，用户需谨慎操作。
- **接口地址**: `/api/v1/auth/unbind-huawei-account`
- **方法**: POST
- **需要登录**: 是
- **请求参数**: 
```json
{
  "password": "string"             // 用户密码确认
}
```
- **响应类型**: application/json
- **返回值**：
```json
{
  "code": 200,
  "message": "华为账号解绑成功",
  "data": {
    "unbindTime": "2024-01-01T00:00:00Z"         // 解绑时间
  }
}
```

---

## 统一错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 200 | 操作成功 | 请求处理成功 |
| 4001 | 华为账号验证失败 | 华为授权令牌无效或过期 |
| 4002 | 华为账号已绑定其他用户 | 账号绑定冲突 |
| 4003 | 设备异常 | 设备信息异常或存在安全风险 |
| 4004 | 参数错误 | 请求参数格式错误或缺失 |
| 4005 | 频率限制 | 请求过于频繁 |
| 4006 | 未授权访问 | 需要登录或Token无效 |
| 4007 | 密码错误 | 用户密码验证失败 |
| 5001 | 服务器内部错误 | 服务器异常 |
| 5002 | 数据库错误 | 数据库操作失败 |
| 5003 | 华为服务异常 | 华为账号服务不可用 |

---

## 接口使用示例

### 华为账号登录示例

```javascript
// 请求示例
const loginData = {
  huaweiAccountId: "huawei_123456789",
  displayName: "张三",
  email: "<EMAIL>",
  phone: "***********",
  authToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  deviceInfo: {
    deviceId: "device_uuid_12345",
    deviceModel: "HUAWEI Mate 50",
    osVersion: "HarmonyOS 3.0",
    appVersion: "1.0.0"
  }
};

fetch('/api/v1/auth/huawei-login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(loginData)
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    // 登录成功，保存token
    localStorage.setItem('token', data.data.token);
    console.log('登录成功:', data.data.user);
  } else {
    console.error('登录失败:', data.message);
  }
});
```

---

**文档版本**: 1.0  
**创建日期**: 2024年1月  
**维护人员**: AI系统自动生成 