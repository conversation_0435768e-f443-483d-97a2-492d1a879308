{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_Storage-and-boxes_0DmWRk80HcvJxAgMjHuHfxNfgSPHEgELsOXCoYKPqG4=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001994831A68167C89E3A38A974EDC595EAFF7AFEC3B8E58E3D4BB2C950910ABC097928CC5FDD7",
          "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_Storage-and-boxes_0DmWRk80HcvJxAgMjHuHfxNfgSPHEgELsOXCoYKPqG4=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_Storage-and-boxes_0DmWRk80HcvJxAgMjHuHfxNfgSPHEgELsOXCoYKPqG4=.p12",
          "storePassword": "000000199ECBB322ECB645A2E1B8CEAA248D39CBD7F90DC5E7593A821633C907D3CA8F051B29002C1D"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.2(14)",
        "runtimeOS": "HarmonyOS",
        "buildOption": {
          "strictMode": {
            "caseSensitiveCheck": true,
            "useNormalizedOHMUrl": true
          }
        }
      }
    ],
    "buildModeSet": [
      {
        "name": "debug",
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}