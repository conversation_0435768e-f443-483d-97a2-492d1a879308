@Component
export struct DatePickerDialog {
  onCancel?: () => void;
  onConfirm?: (date: Date) => void;
  @State selectedDate: Date = new Date();
  
  aboutToAppear() {
    // 默认选择日期为今天
    const today = new Date();
    this.selectedDate = today;
  }
  
  build() {
    Column() {
      // 半透明背景
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('#000000')
        .opacity(0.5)
        .onClick(() => {
          this.onCancel?.();
        })
      
      // 日期选择器内容
      Column() {
        // 标题和按钮
        Row() {
          Button('取消')
            .backgroundColor(Color.Transparent)
            .fontColor('#666666')
            .fontSize(16)
            .onClick(() => {
              this.onCancel?.();
            })
          
          Text('选择有效期')
            .fontSize(18)
            .fontWeight(FontWeight.Medium)
            .layoutWeight(1)
            .textAlign(TextAlign.Center)
          
          Button('确定')
            .backgroundColor(Color.Transparent)
            .fontColor('#A9BE82')
            .fontSize(16)
            .onClick(() => {
              this.onConfirm?.(this.selectedDate);
            })
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 16, bottom: 16 })
        .border({ width: { bottom: 0.5 }, color: '#EEEEEE' })
        
        // 日期选择器
        Row() {
          DatePicker({
            start: new Date('2020-01-01'),
            end: new Date(), // 限制最大日期为今天
            selected: this.selectedDate
          })
            .onDateChange((date) => {
              // 直接使用Date对象，不需要类型转换
              if (date instanceof Date) {
                this.selectedDate = date;
              }
            })
            .width('100%')
            .height(200)
        }
        .width('100%')
      }
      .width('100%')
      .backgroundColor(Color.White)
      .borderRadius({ topLeft: 16, topRight: 16 })
      .position({ x: 0, y: '70%' })
    }
    .width('100%')
    .height('100%')
    .position({ x: 0, y: 0 })
  }
}