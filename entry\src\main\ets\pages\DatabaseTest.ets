import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { DBManager } from '../database/DBManager';
import { MedicineItem } from '../model/MedicineItem';
import { ItemType } from '../model/ItemType';

@Entry
@Component
struct DatabaseTest {
  @State items: MedicineItem[] = [];
  @State searchQuery: string = '';
  @State isLoading: boolean = false;
  @State totalItemCount: number = 0;
  @State totalValue: number = 0;
  @State message: string = '';
  
  // 数据库管理器实例
  private dbManager: DBManager = DBManager.getInstance();

  aboutToAppear() {
    // 初始化数据库
    this.initDatabase();
  }

  /**
   * 初始化数据库
   */
  private initDatabase() {
    this.isLoading = true;
    this.message = '正在初始化数据库...';
    
    // 初始化数据库
    this.dbManager.initDatabase(() => {
      this.message = '数据库初始化成功';
      this.isLoading = false;
      
      // 刷新统计数据
      this.refreshStats();
    });
  }

  /**
   * 导入示例数据
   */
  private importSampleData() {
    this.isLoading = true;
    this.message = '正在导入示例数据...';
    
    // 导入样例数据
    this.dbManager.importSampleDataIfNeeded(() => {
      this.message = '示例数据导入成功';
      this.isLoading = false;
      
      // 刷新统计数据
      this.refreshStats();
    });
  }

  /**
   * 查询所有物品
   */
  private getAllItems() {
    this.isLoading = true;
    this.message = '正在查询所有物品...';
    
    this.dbManager.getAllItems((items: MedicineItem[]) => {
      this.items = items;
      this.message = `查询成功，共找到 ${this.items.length} 个物品`;
      this.isLoading = false;
    });
  }

  /**
   * 添加测试物品
   */
  private addTestItem() {
    this.isLoading = true;
    this.message = '正在添加测试物品...';
    
    // 创建一个测试物品
    const testItem = new MedicineItem(
      '测试物品' + new Date().getTime(),
      '客厅/茶几/上层',
      $r('app.media.add'),
      120,
      ItemType.MEDICINE,
      99.99
    );
    
    // 添加到数据库
    this.dbManager.addItem(testItem, (id: number) => {
      this.message = `测试物品添加成功，ID: ${id}`;
      this.isLoading = false;
      
      // 刷新统计数据
      this.refreshStats();
    });
  }

  /**
   * 搜索物品
   */
  private searchItems() {
    if (this.searchQuery.trim() === '') {
      promptAction.showToast({
        message: '搜索关键词不能为空',
        duration: 2000
      });
      return;
    }
    
    this.isLoading = true;
    this.message = `正在搜索物品: ${this.searchQuery}...`;
    
    this.dbManager.searchItems(this.searchQuery, (items: MedicineItem[]) => {
      this.items = items;
      this.message = `搜索成功，共找到 ${this.items.length} 个物品`;
      this.isLoading = false;
    });
  }

  /**
   * 清空物品列表
   */
  private clearItems() {
    this.items = [];
    this.message = '物品列表已清空';
  }

  /**
   * 刷新统计数据
   */
  private refreshStats() {
    // 获取物品总数
    this.dbManager.getItemCount((count: number) => {
      this.totalItemCount = count;
    });
    
    // 获取物品总价值
    this.dbManager.calculateTotalValue((value: number) => {
      this.totalValue = value;
    });
  }

  build() {
    Column() {
      // 标题栏
      Row() {
        Image($r('app.media.add'))
          .width(24)
          .height(24)
          .margin({ left: 16 })
          .onClick(() => {
            router.back();
          })
        
        Text('数据库测试')
          .fontSize(20)
          .fontWeight(FontWeight.Bold)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)
        
        Blank()
          .width(56)
      }
      .width('100%')
      .height(56)
      .backgroundColor('#FFFFFF')
      
      // 搜索栏
      Row() {
        TextInput({ placeholder: '搜索物品', text: this.searchQuery })
          .width('80%')
          .height(44)
          .borderRadius(22)
          .backgroundColor('#f5f5f5')
          .padding({ left: 16, right: 16 })
          .onChange((value: string) => {
            this.searchQuery = value;
          })
        
        Button('搜索')
          .width('18%')
          .height(44)
          .borderRadius(22)
          .backgroundColor('#007DFF')
          .margin({ left: 8 })
          .onClick(() => {
            this.searchItems();
          })
      }
      .width('90%')
      .padding({ top: 12, bottom: 12 })
      
      // 操作按钮
      Row() {
        Button('初始化数据库')
          .width(120)
          .height(36)
          .backgroundColor('#007DFF')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            this.initDatabase();
          })
        
        Button('导入示例数据')
          .width(120)
          .height(36)
          .backgroundColor('#007DFF')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            this.importSampleData();
          })
        
        Button('添加测试物品')
          .width(120)
          .height(36)
          .backgroundColor('#007DFF')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            this.addTestItem();
          })
      }
      .width('90%')
      .margin({ top: 8, bottom: 8 })
      .justifyContent(FlexAlign.SpaceBetween)
      
      Row() {
        Button('查询所有物品')
          .width(120)
          .height(36)
          .backgroundColor('#007DFF')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            this.getAllItems();
          })
        
        Button('清空物品列表')
          .width(120)
          .height(36)
          .backgroundColor('#ff4d4f')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            this.clearItems();
          })
        
        Button('返回首页')
          .width(120)
          .height(36)
          .backgroundColor('#52c41a')
          .borderRadius(18)
          .fontSize(14)
          .onClick(() => {
            router.back();
          })
      }
      .width('90%')
      .margin({ bottom: 16 })
      .justifyContent(FlexAlign.SpaceBetween)
      
      // 统计信息
      Row() {
        Column() {
          Text('物品总数')
            .fontSize(14)
            .fontColor('#666666')
          
          Text(this.totalItemCount.toString())
            .fontSize(24)
            .fontWeight(FontWeight.Bold)
            .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
        
        Divider()
          .height(40)
          .width(1)
          .color('#E0E0E0')
        
        Column() {
          Text('总价值(元)')
            .fontSize(14)
            .fontColor('#666666')
          
          Text(this.totalValue.toFixed(2))
            .fontSize(24)
            .fontWeight(FontWeight.Bold)
            .margin({ top: 4 })
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
      }
      .width('90%')
      .height(80)
      .borderRadius(8)
      .backgroundColor('#FFFFFF')
      .margin({ bottom: 16 })
      
      // 状态信息
      Text(this.message)
        .width('90%')
        .fontSize(14)
        .fontColor('#666666')
        .margin({ bottom: 12 })
      
      // 加载指示器
      if (this.isLoading) {
        LoadingProgress()
          .width(36)
          .height(36)
          .color('#007DFF')
      }
      
      // 物品列表
      List() {
        ForEach(this.items, (item: MedicineItem) => {
          ListItem() {
            Row() {
              Column() {
                Text(item.name)
                  .fontSize(16)
                  .fontWeight(FontWeight.Bold)
                  .alignSelf(ItemAlign.Start)
                
                Text(`位置: ${item.location}`)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
                
                Text(`保质期: ${item.expireDay} 天`)
                  .fontSize(14)
                  .fontColor('#666666')
                  .alignSelf(ItemAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
              
              Text(`¥${item.price.toFixed(2)}`)
                .fontSize(16)
                .fontWeight(FontWeight.Bold)
                .fontColor('#f5222d')
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .padding({ top: 12, bottom: 12 })
          }
          .width('100%')
          .borderRadius(8)
          .backgroundColor('#FFFFFF')
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 8 })
        })
      }
      .width('90%')
      .layoutWeight(1)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#f5f5f5')
  }
} 