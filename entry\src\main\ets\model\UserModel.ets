/**
 * 用户数据模型
 */
export class UserModel {
  username: string = '';
  password: string = '';
  isAgreeProtocol: boolean = false;

  constructor(username: string = '', password: string = '', isAgreeProtocol: boolean = false) {
    this.username = username;
    this.password = password;
    this.isAgreeProtocol = isAgreeProtocol;
  }
}

/**
 * 登录结果模型
 */
export class LoginResult {
  isSuccess: boolean = false;
  message: string = '';

  constructor(isSuccess: boolean = false, message: string = '') {
    this.isSuccess = isSuccess;
    this.message = message;
  }
}

/**
 * 华为账号信息模型
 */
export class HuaweiAccountInfo {
  accountId: string = '';
  displayName: string = '';
  email: string = '';

  constructor(accountId: string = '', displayName: string = '', email: string = '') {
    this.accountId = accountId;
    this.displayName = displayName;
    this.email = email;
  }
}

/**
 * 华为账号登录结果接口
 */
export interface HuaweiLoginResponse {
  result: LoginResult;
  accountInfo?: HuaweiAccountInfo;
}

/**
 * 用户服务类，模拟网络请求
 */
export class UserService {
  /**
   * 模拟登录请求
   * @param username 用户名
   * @param password 密码
   * @returns 登录结果
   */
  login(username: string, password: string): Promise<LoginResult> {
    return new Promise<LoginResult>((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        // 简单的验证逻辑
        if (username.length > 0 && password.length > 0) {
          resolve(new LoginResult(true, '登录成功'));
        } else {
          resolve(new LoginResult(false, '用户名或密码不能为空'));
        }
      }, 1000);
    });
  }

  /**
   * 模拟华为账号登录请求
   * @returns 登录结果和华为账号信息
   */
  huaweiAccountLogin(): Promise<HuaweiLoginResponse> {
    return new Promise<HuaweiLoginResponse>((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        // 模拟成功获取华为账号信息
        const accountInfo = new HuaweiAccountInfo(
          'huawei_123456',
          '华为用户',
          '<EMAIL>'
        );
        const response: HuaweiLoginResponse = {
          result: new LoginResult(true, '华为账号登录成功'),
          accountInfo: accountInfo
        };
        resolve(response);
      }, 1500);
    });
  }

  /**
   * 模拟注册请求
   * @param username 用户名
   * @param password 密码
   * @returns 注册结果
   */
  register(username: string, password: string): Promise<LoginResult> {
    return new Promise<LoginResult>((resolve) => {
      // 模拟网络延迟
      setTimeout(() => {
        if (username.length > 0 && password.length > 0) {
          resolve(new LoginResult(true, '注册成功'));
        } else {
          resolve(new LoginResult(false, '用户名或密码不能为空'));
        }
      }, 1000);
    });
  }
}