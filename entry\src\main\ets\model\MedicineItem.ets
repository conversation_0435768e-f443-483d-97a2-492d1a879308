/*
物品数据模型
 */

import { ItemType } from './ItemType';
import { DBConstants } from '../database/DBConstants';
import relationalStore from '@ohos.data.relationalStore';
import { Item } from '../common/utils/NotificationManager';

/**
 * 物品信息模型
 */
export class MedicineItem implements Item {
  // 物品ID
  id?: number;
  // 物品名称
  name: string;
  // 物品存放位置
  location: string;
  // 物品图片
  icon: ResourceStr;
  // 保质期天数
  expireDay: number;
  // 保质期单位 ("month" 或 "day")
  expiryUnit: string;
  // 物品类型
  itemType: ItemType;
  // 物品价格
  price: number;
  // 购买日期
  purchaseDate?: number;
  // 生产日期
  productionDate?: number;
  // 创建时间
  createTime?: number;
  // 更新时间
  updateTime?: number;

  constructor(
    name: string,
    location: string,
    icon: ResourceStr,
    expireDay: number,
    itemType: ItemType,
    price: number,
    expiryUnit: string = "month"
  ) {
    this.name = name;
    this.location = location;
    this.icon = icon;
    this.expireDay = expireDay;
    this.expiryUnit = expiryUnit;
    this.itemType = itemType;
    this.price = price;
    // 设置默认时间
    const now = new Date().getTime();
    this.purchaseDate = now;
    this.productionDate = now;
    this.createTime = now;
    this.updateTime = now;
  }

  /**
   * 将数据库记录转换为物品对象
   * @param record 数据库记录
   * @returns 物品对象
   */
  static fromResultSet(record: relationalStore.ResultSet): MedicineItem | null {
    if (!record || record.rowCount <= 0) {
      return null;
    }
    
    const item = new MedicineItem('', '', $r('app.media.add'), 0, ItemType.GENERAL, 0);
    
    const idIndex = record.getColumnIndex(DBConstants.COLUMN_ID);
    if (idIndex >= 0) {
      item.id = record.getDouble(idIndex);
    }
    
    const nameIndex = record.getColumnIndex(DBConstants.COLUMN_NAME);
    if (nameIndex >= 0) {
      item.name = record.getString(nameIndex);
    }
    
    const locationIndex = record.getColumnIndex(DBConstants.COLUMN_LOCATION);
    if (locationIndex >= 0) {
      item.location = record.getString(locationIndex);
    }
    
    const expireDayIndex = record.getColumnIndex(DBConstants.COLUMN_EXPIRY_DAYS);
    if (expireDayIndex >= 0) {
      item.expireDay = record.getDouble(expireDayIndex);
    }
    
    const expiryUnitIndex = record.getColumnIndex(DBConstants.COLUMN_EXPIRY_UNIT);
    if (expiryUnitIndex >= 0) {
      item.expiryUnit = record.getString(expiryUnitIndex);
    }
    
    const itemTypeIndex = record.getColumnIndex(DBConstants.COLUMN_ITEM_TYPE);
    if (itemTypeIndex >= 0) {
      item.itemType = record.getDouble(itemTypeIndex) as ItemType;
    }
    
    const priceIndex = record.getColumnIndex(DBConstants.COLUMN_PRICE);
    if (priceIndex >= 0) {
      item.price = record.getDouble(priceIndex);
    }
    
    const purchaseDateIndex = record.getColumnIndex(DBConstants.COLUMN_PURCHASE_DATE);
    if (purchaseDateIndex >= 0) {
      item.purchaseDate = record.getDouble(purchaseDateIndex);
    }
    
    const productionDateIndex = record.getColumnIndex(DBConstants.COLUMN_PRODUCTION_DATE);
    if (productionDateIndex >= 0) {
      item.productionDate = record.getDouble(productionDateIndex);
    }
    
    const createTimeIndex = record.getColumnIndex(DBConstants.COLUMN_CREATE_TIME);
    if (createTimeIndex >= 0) {
      item.createTime = record.getDouble(createTimeIndex);
    }
    
    const updateTimeIndex = record.getColumnIndex(DBConstants.COLUMN_UPDATE_TIME);
    if (updateTimeIndex >= 0) {
      item.updateTime = record.getDouble(updateTimeIndex);
    }
    
    return item;
  }

  /**
   * 将物品对象转换为数据库值
   * @returns ValuesBucket对象
   */
  toValuesBucket(): relationalStore.ValuesBucket {
    const values: relationalStore.ValuesBucket = {};
    
    values[DBConstants.COLUMN_NAME] = this.name;
    values[DBConstants.COLUMN_LOCATION] = this.location;
    values[DBConstants.COLUMN_EXPIRY_DAYS] = this.expireDay;
    values[DBConstants.COLUMN_EXPIRY_UNIT] = this.expiryUnit;
    values[DBConstants.COLUMN_ITEM_TYPE] = this.itemType;
    values[DBConstants.COLUMN_PRICE] = this.price;
    
    const now = new Date().getTime();
    
    if (this.purchaseDate) {
      values[DBConstants.COLUMN_PURCHASE_DATE] = this.purchaseDate;
    } else {
      values[DBConstants.COLUMN_PURCHASE_DATE] = now;
    }
    
    if (this.productionDate) {
      values[DBConstants.COLUMN_PRODUCTION_DATE] = this.productionDate;
    } else {
      values[DBConstants.COLUMN_PRODUCTION_DATE] = now;
    }
    
    if (this.createTime) {
      values[DBConstants.COLUMN_CREATE_TIME] = this.createTime;
    } else {
      values[DBConstants.COLUMN_CREATE_TIME] = now;
    }
    
    values[DBConstants.COLUMN_UPDATE_TIME] = now;
    
    return values;
  }
}