/**
 * 主题颜色接口
 */
export interface ThemeColors {
  // 背景色
  PAGE_BACKGROUND: string;
  CARD_BACKGROUND: string;
  SIDEBAR_BACKGROUND: string;
  DIALOG_BACKGROUND: string;
  
  // 前景色
  PRIMARY_TEXT: string;
  SECONDARY_TEXT: string;
  HINT_TEXT: string;
  
  // 强调色
  PRIMARY_COLOR: string;
  PRIMARY_COLOR_PRESSED: string;
  
  // 边框、分割线
  DIVIDER_COLOR: string;
  BORDER_COLOR: string;
  
  // 其他颜色
  SUCCESS_COLOR: string;
  WARNING_COLOR: string;
  ERROR_COLOR: string;
  
  // 按钮颜色
  BUTTON_TEXT: string;
  
  // 特殊元素
  TOAST_BACKGROUND: string;
  TOAST_TEXT: string;
  MASK_COLOR: string;
  
  // 图标颜色
  ICON_COLOR: string;
  
  // 列表项颜色
  ACTIVE_ITEM_BG: string;
}

/**
 * 主题常量定义
 */
export class ThemeConstants {
  // 主题类型
  static readonly THEME_LIGHT: string = 'light';
  static readonly THEME_DARK: string = 'dark';
  static readonly THEME_AUTO: string = 'auto';
  
  // 浅色主题颜色
  static readonly LIGHT_COLORS: ThemeColors = {
    // 背景色
    PAGE_BACKGROUND: '#F5F7FA',
    CARD_BACKGROUND: '#FFFFFF',
    SIDEBAR_BACKGROUND: '#FFFFFF',
    DIALOG_BACKGROUND: '#FFFFFF',
    
    // 前景色
    PRIMARY_TEXT: '#333333',
    SECONDARY_TEXT: '#666666',
    HINT_TEXT: '#999999',
    
    // 强调色
    PRIMARY_COLOR: '#B8E986',
    PRIMARY_COLOR_PRESSED: '#A9DE6B',
    
    // 边框、分割线
    DIVIDER_COLOR: '#F0F0F0',
    BORDER_COLOR: '#EEEEEE',
    
    // 其他颜色
    SUCCESS_COLOR: '#4CAF50',
    WARNING_COLOR: '#FFA940',
    ERROR_COLOR: '#FF7D6B',
    
    // 按钮颜色
    BUTTON_TEXT: '#FFFFFF',
    
    // 特殊元素
    TOAST_BACKGROUND: 'rgba(0, 0, 0, 0.7)',
    TOAST_TEXT: '#FFFFFF',
    MASK_COLOR: 'rgba(0, 0, 0, 0.5)',
    
    // 图标颜色
    ICON_COLOR: '#666666',
    
    // 列表项颜色
    ACTIVE_ITEM_BG: '#F0F7FF'
  };
  
  // 深色主题颜色
  static readonly DARK_COLORS: ThemeColors = {
    // 背景色
    PAGE_BACKGROUND: '#121212',
    CARD_BACKGROUND: '#1E1E1E',
    SIDEBAR_BACKGROUND: '#252525',
    DIALOG_BACKGROUND: '#2C2C2C',
    
    // 前景色
    PRIMARY_TEXT: '#FFFFFF',
    SECONDARY_TEXT: '#CCCCCC',
    HINT_TEXT: '#999999',
    
    // 强调色
    PRIMARY_COLOR: '#98CB67',
    PRIMARY_COLOR_PRESSED: '#7FB446',
    
    // 边框、分割线
    DIVIDER_COLOR: '#333333',
    BORDER_COLOR: '#444444',
    
    // 其他颜色
    SUCCESS_COLOR: '#66BB6A',
    WARNING_COLOR: '#FFB74D',
    ERROR_COLOR: '#FF8A80',
    
    // 按钮颜色
    BUTTON_TEXT: '#FFFFFF',
    
    // 特殊元素
    TOAST_BACKGROUND: 'rgba(255, 255, 255, 0.9)',
    TOAST_TEXT: '#000000',
    MASK_COLOR: 'rgba(0, 0, 0, 0.7)',
    
    // 图标颜色
    ICON_COLOR: '#AAAAAA',
    
    // 列表项颜色
    ACTIVE_ITEM_BG: '#2C3345'
  };
} 