import { ThemeConstants, ThemeColors } from '../constants/ThemeConstants';
import { ThemeManager } from '../utils/ThemeManager';

/**
 * 主题提供者组件
 * 作为应用的根组件，提供主题上下文
 */
@Component
export struct ThemeProvider {
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  @BuilderParam content: () => void;
  
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  aboutToAppear() {
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      // 加载当前主题设置
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      const isDark = await this.themeManager.isDarkMode();
      this.updateTheme(isDark);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('主题更新', isDark ? '深色模式' : '浅色模式');
  }
  
  build() {
    Column() {
      // 在此包裹子组件，并将主题上下文传递给子组件
      this.content();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
  }
} 