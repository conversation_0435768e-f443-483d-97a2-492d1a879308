# Set environment variables
$env:DEVECO_SDK_HOME = "D:\4.PC_software\DevEco Studio"

Write-Host "Starting build with HarmonyOS DevEco Studio tools..."

# Use call operator (&) to execute command
try {
    & "D:\4.PC_software\DevEco Studio\tools\node\node.exe" "D:\4.PC_software\DevEco Studio\tools\hvigor\bin\hvigorw.js" --mode module -p module=entry@default -p product=default -p requiredDeviceType=phone assembleHap --analyze=verbose --parallel --incremental --daemon
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Build completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Build failed with exit code $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "Error executing build command: $_" -ForegroundColor Red
} 