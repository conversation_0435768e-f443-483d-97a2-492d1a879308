# 搜索界面自动隐藏功能

## 功能概述

当用户在物品管家应用中使用搜索功能时，点击搜索框会显示虚拟键盘和分类筛选气泡。为了提供更好的用户体验，我们添加了自动隐藏功能：**当用户点击非搜索功能区域时，键盘和分类框会自动消失**。

## 功能实现

### 1. 核心方法

在 `Home.ets` 中添加了 `hideSearchInterface()` 方法：

```typescript
/**
 * 隐藏搜索相关界面
 */
hideSearchInterface() {
  this.showSearchBubbles = false;
  // 隐藏软键盘
  try {
    let inputMethodController = inputMethod.getController();
    inputMethodController.stopInputSession();
  } catch (err) {
    console.error('隐藏软键盘失败:', err);
  }
}
```

### 2. 触发区域

以下区域被设置为点击后自动隐藏搜索界面：

- **顶部导航栏**：点击"物品管家"标题区域
- **资产信息卡片**：点击显示用户资产的卡片区域
- **分类标签行**：点击"全部"、"已过期"、"临期"、"正常"标签区域
- **物品列表**：点击物品列表的任意位置

### 3. 实现细节

1. **键盘隐藏**：使用 `inputMethod.getController().stopInputSession()` 停止输入会话，系统自动隐藏虚拟键盘
2. **分类框隐藏**：设置 `showSearchBubbles = false` 隐藏分类筛选气泡
3. **用户体验**：点击事件不会干扰原有功能，例如点击物品仍然可以正常打开详情页面

## 使用场景

### 场景1：搜索后查看结果
1. 用户点击搜索框
2. 显示键盘和分类筛选选项
3. 用户输入搜索内容或选择筛选条件
4. 用户点击物品列表区域查看搜索结果
5. **键盘和分类框自动隐藏**，界面更简洁

### 场景2：取消搜索操作
1. 用户点击搜索框（误操作）
2. 显示键盘和分类筛选选项
3. 用户点击其他任意区域（如导航栏、资产卡片等）
4. **键盘和分类框自动隐藏**，回到正常界面

### 场景3：搜索中途切换操作
1. 用户正在搜索物品
2. 想要查看资产信息或切换分类标签
3. 点击对应区域时
4. **搜索界面自动隐藏**，不影响新操作

## 技术要点

### 导入依赖
```typescript
import inputMethod from '@ohos.inputMethod';
```

### 状态管理
- `showSearchBubbles`: 控制分类筛选气泡的显示/隐藏
- `searchText`: 搜索文本内容（保持不变）
- `isSearching`: 搜索状态标识（保持不变）

### 事件处理
每个需要隐藏搜索界面的UI组件都添加了 `.onClick()` 事件：

```typescript
.onClick(() => {
  // 点击XX区域时隐藏搜索界面
  this.hideSearchInterface();
})
```

## 测试覆盖

创建了完整的单元测试 `SearchHideFunction.test.ets`，包含以下测试用例：

1. 搜索气泡隐藏功能测试
2. 点击导航栏隐藏搜索功能测试
3. 点击资产卡片隐藏搜索功能测试
4. 点击分类标签隐藏搜索功能测试
5. 点击物品列表隐藏搜索功能测试
6. 搜索状态管理测试
7. 焦点控制测试

## 兼容性说明

- **HarmonyOS API 9+**：使用了 `@kit.ArkUI` 中的 `focusControl`
- **向后兼容**：新增功能不会影响现有搜索功能
- **无副作用**：点击隐藏操作不会清空搜索内容，用户可以继续编辑

## 用户体验改进

1. **减少误操作**：避免键盘意外弹出影响界面操作
2. **界面简洁**：及时隐藏不需要的UI元素
3. **操作流畅**：无需手动关闭键盘，提高操作效率
4. **符合预期**：符合移动应用的通用交互模式

这个功能让用户在使用搜索功能时更加便捷，提供了更好的移动端交互体验。