# Storage and Boxes 应用开发文档

## 1. 项目概述

Storage and Boxes（物匣）是一款基于 HarmonyOS/ArkTS 开发的物品存储管理应用，帮助用户记录和管理家庭/个人物品的存储位置、数量和到期时间等信息。应用采用了 MVVM 架构模式，支持用户注册登录、物品管理、过期提醒、主题切换等功能。

## 2. 技术栈

- **开发平台**: HarmonyOS DevEco Studio
- **开发语言**: ArkTS (TypeScript)
- **数据库**: 关系型数据库 (RDB)
- **架构模式**: MVVM (Model-View-ViewModel)
- **UI框架**: ArkUI
- **权限**: 振动权限 (ohos.permission.VIBRATE)

## 3. 应用主要功能

### 3.1 用户认证系统

- **用户注册**：支持用户名、密码注册
- **用户登录**：支持普通登录和华为账号一键登录
- **密码管理**：忘记密码、重置密码功能
- **协议确认**：用户协议和隐私政策确认

### 3.2 核心功能

- **物品管理**：添加、编辑、删除和查询存储的物品
- **分类管理**：根据物品类型进行分类管理（药品、食品、电子产品等）
- **位置管理**：三级位置管理（区域/家具/具体位置）
- **过期提醒**：基于物品保质期的过期提醒功能
- **搜索功能**：支持物品名称、类型和位置的搜索
- **主题系统**：支持亮色/暗色主题切换及跟随系统主题
- **个人设置**：支持用户名设置和多页面同步

### 3.3 特色功能

- **数据库测试**：提供数据库操作测试页面
- **通知管理**：过期物品通知和提醒功能
- **自定义分类**：用户可自定义物品分类类型
- **自定义位置**：支持用户自定义三级位置体系
- **资产统计**：统计所有物品的总价值
- **批量操作**：支持批量添加和编辑物品
- **深浅色模式**：支持手动切换深浅色模式或跟随系统设置
- **用户数据同步**：在不同页面间实时同步用户配置数据

## 4. 技术架构

### 4.1 应用架构

应用采用 MVVM（Model-View-ViewModel）架构模式，并结合单例模式管理全局数据：

- **Model**：定义数据结构和业务逻辑
  - `MedicineItem.ets`：物品数据模型
  - `ItemType.ets`：物品类型模型
  - `UserModel.ets`：用户数据模型

- **View**：用户界面页面
  - `Login.ets`：登录页面
  - `Register.ets`：注册页面
  - `Home.ets`：主页面组件
  - `Settings.ets`：设置页面组件
  - `AddItem.ets`：添加物品页面
  - `DatabaseTest.ets`：数据库测试页面
  - `Notifications.ets`：通知页面

- **ViewModel**：连接 View 和 Model，处理业务逻辑
  - `LoginViewModel.ets`：登录页面视图模型
  - `RegisterViewModel.ets`：注册页面视图模型
  - `ForgotPasswordViewModel.ets`：忘记密码视图模型
  - `ItemDataService.ets`：物品数据服务

- **Database**：数据库层
  - `DBManager.ets`：数据库管理器（单例）
  - `DBHelper.ets`：数据库操作帮助类
  - `DBConstants.ets`：数据库常量定义

- **Common**：公共组件和工具
  - `ThemeManager.ets`：主题管理器（单例）
  - `UserManager.ets`：用户信息管理器（单例）
  - `NotificationManager.ets`：通知管理器
  - `ThemeProvider.ets`：主题提供者组件
  - `Logger.ets`：日志工具

### 4.2 目录结构

```
entry/
├── src/
│   ├── main/
│   │   ├── ets/
│   │   │   ├── pages/                    # 页面组件
│   │   │   │   ├── Login.ets             # 登录页面
│   │   │   │   ├── Register.ets          # 注册页面
│   │   │   │   ├── Index.ets             # 首页（跳转页）
│   │   │   │   ├── Home.ets              # 主页面
│   │   │   │   ├── Settings.ets          # 设置页面
│   │   │   │   ├── AddItem.ets           # 添加物品页面
│   │   │   │   ├── DatabaseTest.ets      # 数据库测试页面
│   │   │   │   ├── Notifications.ets     # 通知页面
│   │   │   │   ├── ForgotPassword.ets    # 忘记密码页面
│   │   │   │   ├── ResetPassword.ets     # 重置密码页面
│   │   │   │   ├── WebView.ets           # 网页视图页面
│   │   │   │   └── NotificationDetail.ets # 通知详情页面
│   │   │   ├── model/                    # 数据模型
│   │   │   │   ├── MedicineItem.ets      # 物品数据模型
│   │   │   │   ├── ItemType.ets          # 物品类型模型
│   │   │   │   ├── UserModel.ets         # 用户数据模型
│   │   │   │   └── HuaweiAccountService.ets # 华为账号服务
│   │   │   ├── viewmodel/                # 视图模型
│   │   │   │   ├── LoginViewModel.ets    # 登录视图模型
│   │   │   │   ├── RegisterViewModel.ets # 注册视图模型
│   │   │   │   ├── ForgotPasswordViewModel.ets # 忘记密码视图模型
│   │   │   │   └── ItemDataService.ets   # 物品数据服务
│   │   │   ├── database/                 # 数据库层
│   │   │   │   ├── DBManager.ets         # 数据库管理器
│   │   │   │   ├── DBHelper.ets          # 数据库操作帮助类
│   │   │   │   └── DBConstants.ets       # 数据库常量
│   │   │   ├── common/                   # 公共组件和工具
│   │   │   │   ├── Logger.ets            # 日志工具
│   │   │   │   ├── constants/            # 常量定义
│   │   │   │   │   └── ThemeConstants.ets # 主题常量
│   │   │   │   ├── components/           # 公共组件
│   │   │   │   │   └── ThemeProvider.ets # 主题提供者组件
│   │   │   │   └── utils/                # 工具类
│   │   │   │       ├── ThemeManager.ets  # 主题管理器
│   │   │   │       ├── UserManager.ets   # 用户信息管理器
│   │   │   │       └── NotificationManager.ets # 通知管理器
│   │   │   ├── components/               # 自定义组件
│   │   │   │   └── DatePickerDialog.ets  # 日期选择器对话框
│   │   │   ├── entryability/             # 入口能力
│   │   │   │   └── EntryAbility.ets      # 应用入口
│   │   │   └── entrybackupability/       # 备份能力
│   │   │       └── EntryBackupAbility.ets # 备份入口
│   │   ├── resources/                    # 资源文件
│   │   │   ├── base/                     # 基础资源
│   │   │   │   ├── element/              # 元素资源
│   │   │   │   ├── media/                # 媒体资源
│   │   │   │   └── profile/              # 配置文件
│   │   │   │       └── main_pages.json  # 页面路由配置
│   │   │   └── ...                       # 其他资源
│   │   └── module.json5                  # 模块配置
│   ├── mock/                             # 模拟数据
│   ├── ohosTest/                         # 测试代码
│   └── test/                             # 单元测试
├── build-profile.json5                   # 构建配置
├── hvigorfile.ts                         # 构建脚本
└── oh-package.json5                      # 包配置
```

## 5. 页面功能详解

### 5.1 登录页面 (Login.ets)

**功能描述**：用户登录入口页面，支持普通登录和华为账号一键登录

**主要功能**：
- 用户名/密码登录
- 华为账号一键登录
- 用户协议确认
- 跳转到注册页面
- 跳转到忘记密码页面

**接口方法**：
- `handleLogin()`: 处理普通登录
- `handleHuaweiLogin()`: 处理华为账号登录
- `navigateToRegister()`: 跳转到注册页面
- `navigateToForgotPassword()`: 跳转到忘记密码页面

### 5.2 注册页面 (Register.ets)

**功能描述**：用户注册页面

**主要功能**：
- 用户名注册
- 密码设置和确认
- 表单验证
- 用户协议确认

### 5.3 主页面 (Home.ets)

**功能描述**：应用的核心页面，展示物品列表和管理功能

**主要功能**：
- **顶部导航栏**：显示应用标题和菜单按钮
- **统计卡片**：显示物品总数和总价值
- **标签页切换**：全部物品、已过期物品分类显示
- **物品列表**：展示所有物品，支持点击查看详情
- **搜索功能**：支持物品名称搜索
- **添加物品**：快速添加新物品
- **侧边菜单**：用户信息和设置入口

**核心方法**：
- `loadItemsFromDB()`: 从数据库加载物品数据
- `addItemToDB(item)`: 添加物品到数据库
- `updateItemInDB(item)`: 更新数据库中的物品
- `deleteItemFromDB(id)`: 从数据库删除物品
- `searchItems(keyword)`: 搜索物品
- `calculateTotalAssets()`: 计算总资产
- `checkExpiryItems()`: 检查过期物品

### 5.4 设置页面 (Settings.ets)

**功能描述**：应用设置和用户配置页面

**主要功能**：
- 主题模式切换（亮色/暗色/跟随系统）
- 用户名修改
- 应用信息展示
- 其他设置选项

### 5.5 添加物品页面 (AddItem.ets)

**功能描述**：专门的物品添加页面

**主要功能**：
- 物品信息录入
- 位置选择器
- 类型选择器
- 日期选择器
- 表单验证和保存

### 5.6 数据库测试页面 (DatabaseTest.ets)

**功能描述**：数据库操作测试和调试页面

**主要功能**：
- 导入示例数据
- 查询所有物品
- 搜索物品测试
- 统计数据显示
- 数据库操作日志

**核心方法**：
- `importSampleData()`: 导入示例数据
- `getAllItems()`: 查询所有物品
- `searchItems()`: 搜索物品
- `refreshStats()`: 刷新统计数据

### 5.7 通知页面 (Notifications.ets)

**功能描述**：显示过期提醒和系统通知

**主要功能**：
- 过期物品通知列表
- 通知详情查看
- 通知状态管理
- 通知设置

### 5.8 忘记密码页面 (ForgotPassword.ets)

**功能描述**：密码重置功能页面

**主要功能**：
- 用户身份验证
- 密码重置流程
- 验证码发送和验证

### 5.9 重置密码页面 (ResetPassword.ets)

**功能描述**：新密码设置页面

**主要功能**：
- 新密码输入
- 密码确认
- 密码强度验证

### 5.10 网页视图页面 (WebView.ets)

**功能描述**：内嵌网页显示页面

**主要功能**：
- 用户协议显示
- 隐私政策显示
- 帮助文档显示

## 6. 数据模型详解

### 6.1 MedicineItem 类 (物品数据模型)

**文件位置**: `entry/src/main/ets/model/MedicineItem.ets`

**功能描述**: 定义物品的数据结构和数据库操作方法

**主要属性**:
```typescript
export class MedicineItem implements Item {
  id?: number;                    // 物品ID（数据库主键）
  name: string;                   // 物品名称
  location: string;               // 物品存放位置
  icon: ResourceStr;              // 物品图标
  expireDay: number;              // 保质期天数
  expiryUnit: string;             // 保质期单位 ("month" 或 "day")
  itemType: ItemType;             // 物品类型
  price: number;                  // 物品价格
  purchaseDate?: number;          // 购买日期（时间戳）
  productionDate?: number;        // 生产日期（时间戳）
  createTime?: number;            // 创建时间（时间戳）
  updateTime?: number;            // 更新时间（时间戳）
}
```

**核心方法**:
- `static fromResultSet(record)`: 从数据库记录转换为物品对象
- `toValuesBucket()`: 将物品对象转换为数据库值对象

### 6.2 ItemType 枚举 (物品类型)

**文件位置**: `entry/src/main/ets/model/ItemType.ets`

**功能描述**: 定义物品类型枚举和相关工具方法

**物品类型定义**:
```typescript
export enum ItemType {
  GENERAL = 0,      // 普通物品
  MEDICINE = 1,     // 药品
  FOOD = 2,         // 食品
  VEGETABLE = 3,    // 蔬菜
  FRUIT = 4,        // 水果
  MEAT = 5,         // 肉类
  SEAFOOD = 6,      // 海鲜
  EGG = 7,          // 蛋类
  MILK = 8,         // 乳制品
  ELECTRONIC = 9,   // 电子产品
  CLOTHING = 10,    // 服装
  BOOK = 11,        // 书籍
  TOY = 12,         // 玩具
  COSMETIC = 13,    // 化妆品
  CLEANING = 14,    // 清洁用品
  STATIONERY = 15,  // 文具
  TOOL = 16,        // 工具
  DOCUMENT = 17,    // 文档
  JEWELRY = 18,     // 珠宝
  LUGGAGE = 19,     // 行李箱包
  SPORTS = 20,      // 运动用品
  DECORATION = 21,  // 装饰品
  OTHER = 22        // 其他
}
```

**工具方法**:
- `getItemTypeName(type)`: 获取物品类型名称
- `getItemTypeIcon(type)`: 获取物品类型图标
- `getAllItemTypes()`: 获取所有物品类型（包括自定义）
- `addCustomItemType(name, icon)`: 添加自定义物品类型

### 6.3 UserModel 类 (用户数据模型)

**文件位置**: `entry/src/main/ets/model/UserModel.ets`

**功能描述**: 定义用户相关的数据结构和服务

**用户数据模型**:
```typescript
export class UserModel {
  username: string = '';           // 用户名
  password: string = '';           // 密码
  isAgreeProtocol: boolean = false; // 是否同意协议
}
```

**登录结果模型**:
```typescript
export class LoginResult {
  isSuccess: boolean = false;      // 登录是否成功
  message: string = '';            // 结果消息
}
```

**华为账号信息模型**:
```typescript
export class HuaweiAccountInfo {
  accountId: string = '';          // 账号ID
  displayName: string = '';        // 显示名称
  email: string = '';              // 邮箱地址
}
```

**用户服务类**:
```typescript
export class UserService {
  login(username, password): Promise<LoginResult>           // 普通登录
  huaweiAccountLogin(): Promise<HuaweiLoginResponse>        // 华为账号登录
  register(username, password): Promise<LoginResult>       // 用户注册
}
```

## 7. 数据库系统

### 7.1 DBManager 类 (数据库管理器)

**文件位置**: `entry/src/main/ets/database/DBManager.ets`

**功能描述**: 数据库操作的统一管理器，采用单例模式

**核心方法**:
```typescript
export class DBManager {
  static getInstance(): DBManager                           // 获取单例实例
  initDatabase(callback?: Function): void                   // 初始化数据库
  importSampleDataIfNeeded(callback: () => void): void     // 导入示例数据
  addItem(item: MedicineItem, callback?: Function): void   // 添加物品
  batchAddItems(items: MedicineItem[], callback?: Function): void // 批量添加物品
  updateItem(id: number, item: MedicineItem, callback?: Function): void // 更新物品
  deleteItem(id: number, callback?: Function): void        // 删除物品
  getAllItems(callback: Function): void                    // 获取所有物品
  searchItems(keyword: string, callback: Function): void   // 搜索物品
  getItemCount(callback: Function): void                   // 获取物品总数
  calculateTotalValue(callback: Function): void            // 计算总价值
}
```

### 7.2 DBHelper 类 (数据库操作帮助类)

**文件位置**: `entry/src/main/ets/database/DBHelper.ets`

**功能描述**: 提供底层数据库操作方法

**核心方法**:
```typescript
export class DBHelper {
  getRdbStore(callback?: Function): void                   // 获取数据库实例
  insertItem(medicineItem: MedicineItem, callback?: Function): void // 插入物品
  batchInsertItems(medicineItems: Array<MedicineItem>, callback?: Function): void // 批量插入
  updateItem(id: number, medicineItem: MedicineItem, callback?: Function): void // 更新物品
  deleteItem(id: number, callback?: Function): void       // 删除物品
  queryAllItems(callback: Function): void                 // 查询所有物品
  queryItemsByType(itemType: ItemType, callback: Function): void // 按类型查询
  searchItemsByName(keyword: string, callback: Function): void // 按名称搜索
}
```

### 7.3 DBConstants 类 (数据库常量)

**文件位置**: `entry/src/main/ets/database/DBConstants.ets`

**功能描述**: 定义数据库相关常量

**数据库配置**:
```typescript
export class DBConstants {
  static readonly DB_NAME: string = 'storage_box.db';     // 数据库名
  static readonly DB_VERSION: number = 4;                 // 数据库版本
  static readonly ITEM_TABLE: string = 'item';            // 物品表名
}
```

**表字段定义**:
```typescript
// 物品表字段
static readonly COLUMN_ID: string = 'id';                      // 主键ID
static readonly COLUMN_NAME: string = 'name';                  // 物品名称
static readonly COLUMN_LOCATION: string = 'location';          // 存储位置
static readonly COLUMN_EXPIRY_DAYS: string = 'expiry_days';    // 保质期天数
static readonly COLUMN_EXPIRY_UNIT: string = 'expiry_unit';    // 保质期单位
static readonly COLUMN_ITEM_TYPE: string = 'item_type';        // 物品类型
static readonly COLUMN_PRICE: string = 'price';                // 物品价格
static readonly COLUMN_PURCHASE_DATE: string = 'purchase_date'; // 购买日期
static readonly COLUMN_PRODUCTION_DATE: string = 'production_date'; // 生产日期
static readonly COLUMN_CREATE_TIME: string = 'create_time';    // 创建时间
static readonly COLUMN_UPDATE_TIME: string = 'update_time';    // 更新时间
```

**SQL语句**:
```sql
-- 创建物品表
CREATE TABLE IF NOT EXISTS item (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  location TEXT NOT NULL,
  image TEXT,
  expiry_days INTEGER DEFAULT 0,
  expiry_unit TEXT DEFAULT 'month',
  item_type INTEGER DEFAULT 6,
  price REAL DEFAULT 0.0,
  purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  production_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 8. 核心管理器

### 8.1 ThemeManager 类 (主题管理器)

**文件位置**: `entry/src/main/ets/common/utils/ThemeManager.ets`

**功能描述**: 主题管理器负责主题的存储、切换和同步，采用单例模式

**核心方法**:
```typescript
export class ThemeManager {
  static getInstance(): ThemeManager                        // 获取单例实例
  async init(): Promise<void>                              // 初始化主题管理器
  async getTheme(): Promise<string>                        // 获取当前主题
  async setTheme(theme: string): Promise<void>             // 设置主题
  async isDarkMode(): Promise<boolean>                     // 判断当前是否为深色模式
  registerThemeChangeCallback(callback: (isDark: boolean) => void): void // 注册主题变化回调
  unregisterThemeChangeCallback(callback: (isDark: boolean) => void): void // 移除主题变化回调
}
```

**主题常量**:
```typescript
// 主题类型
THEME_LIGHT = 'light'      // 亮色主题
THEME_DARK = 'dark'        // 暗色主题
THEME_AUTO = 'auto'        // 跟随系统
```

### 8.2 UserManager 类 (用户管理器)

**文件位置**: `entry/src/main/ets/common/utils/UserManager.ets`

**功能描述**: 用户管理器负责用户信息的存储和同步，采用单例模式

**核心方法**:
```typescript
export class UserManager {
  static getInstance(): UserManager                        // 获取单例实例
  async init(): Promise<void>                             // 初始化用户管理器
  async getUsername(): Promise<string>                    // 获取用户名
  async setUsername(username: string): Promise<void>      // 设置用户名
  registerUsernameChangeCallback(callback: (username: string) => void): void // 注册用户名变化回调
  unregisterUsernameChangeCallback(callback: (username: string) => void): void // 移除用户名变化回调
}
```

### 8.3 NotificationManager 类 (通知管理器)

**文件位置**: `entry/src/main/ets/common/utils/NotificationManager.ets`

**功能描述**: 管理应用内通知和过期提醒

**核心方法**:
```typescript
export class NotificationManager {
  static getInstance(): NotificationManager               // 获取单例实例
  addNotification(item: Item): void                      // 添加通知
  removeNotification(id: string): void                   // 移除通知
  getNotifications(): Notification[]                     // 获取所有通知
  getUnreadCount(): number                              // 获取未读通知数量
  markAsRead(id: string): void                          // 标记为已读
  clearAllNotifications(): void                         // 清空所有通知
}
```

## 9. 视图模型 (ViewModel)

### 9.1 LoginViewModel 类 (登录视图模型)

**文件位置**: `entry/src/main/ets/viewmodel/LoginViewModel.ets`

**功能描述**: 处理登录页面的业务逻辑和数据管理

**核心方法**:
```typescript
export class LoginViewModel {
  setUsername(username: string): void                     // 设置用户名
  setPassword(password: string): void                     // 设置密码
  setAgreeProtocol(isAgree: boolean): void               // 设置协议同意状态
  validateForm(): FormValidationResult                   // 验证表单
  login(): Promise<LoginResult>                          // 普通登录
  huaweiLogin(): Promise<LoginResult>                    // 华为账号登录
  register(): Promise<LoginResult>                       // 跳转注册
  forgetPassword(): Promise<LoginResult>                 // 忘记密码
}
```

**表单验证接口**:
```typescript
export interface FormValidationResult {
  isValid: boolean;                                      // 验证是否通过
  message: string;                                       // 验证消息
}
```

### 9.2 RegisterViewModel 类 (注册视图模型)

**文件位置**: `entry/src/main/ets/viewmodel/RegisterViewModel.ets`

**功能描述**: 处理用户注册相关的业务逻辑

### 9.3 ForgotPasswordViewModel 类 (忘记密码视图模型)

**文件位置**: `entry/src/main/ets/viewmodel/ForgotPasswordViewModel.ets`

**功能描述**: 处理密码重置相关的业务逻辑

### 9.4 ItemDataService 类 (物品数据服务)

**文件位置**: `entry/src/main/ets/viewmodel/ItemDataService.ets`

**功能描述**: 提供物品数据相关的业务逻辑服务

## 10. 公共组件和工具

### 10.1 ThemeProvider 组件 (主题提供者)

**文件位置**: `entry/src/main/ets/common/components/ThemeProvider.ets`

**功能描述**: 为应用提供统一的主题上下文，包装子组件并提供主题相关的状态和方法

### 10.2 DatePickerDialog 组件 (日期选择器对话框)

**文件位置**: `entry/src/main/ets/components/DatePickerDialog.ets`

**功能描述**: 自定义日期选择器对话框组件

### 10.3 Logger 工具 (日志工具)

**文件位置**: `entry/src/main/ets/common/Logger.ets`

**功能描述**: 统一的日志输出工具，提供不同级别的日志记录

**核心方法**:
```typescript
class Logger {
  static info(tag: string, message: string): void        // 信息日志
  static error(tag: string, message: string): void       // 错误日志
  static warn(tag: string, message: string): void        // 警告日志
  static debug(tag: string, message: string): void       // 调试日志
}
```

### 10.4 ThemeConstants 常量 (主题常量)

**文件位置**: `entry/src/main/ets/common/constants/ThemeConstants.ets`

**功能描述**: 定义主题相关的常量和颜色配置

**主题颜色接口**:
```typescript
export interface ThemeColors {
  PRIMARY_COLOR: string;      // 主色调
  ERROR_COLOR: string;        // 错误颜色
  WARNING_COLOR: string;      // 警告颜色
  SUCCESS_COLOR: string;      // 成功颜色
  PRIMARY_TEXT: string;       // 主要文本颜色
  SECONDARY_TEXT: string;     // 次要文本颜色
  HINT_TEXT: string;          // 提示文本颜色
  CARD_BACKGROUND: string;    // 卡片背景色
  PAGE_BACKGROUND: string;    // 页面背景色
  DIVIDER_COLOR: string;      // 分割线颜色
}
```

## 11. 设计模式应用

### 11.1 单例模式

在多个管理器类中使用单例模式，确保全局只有一个实例：

```typescript
// ThemeManager、UserManager、DBManager、NotificationManager 都采用单例模式
public static getInstance(): ThemeManager {
  if (!ThemeManager.instance) {
    ThemeManager.instance = new ThemeManager();
  }
  return ThemeManager.instance;
}
```

### 11.2 观察者模式

通过回调机制实现观察者模式，使组件能够响应数据变化：

```typescript
// 注册回调
this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);

// 回调函数
private themeChangeCallback = (isDark: boolean) => {
  this.updateTheme(isDark);
};

// 通知回调
private notifyThemeChange(isDark: boolean): void {
  this.themeChangeCallbacks.forEach(callback => {
    callback(isDark);
  });
}
```

### 11.3 MVVM 模式

应用整体采用 MVVM 架构模式：
- **Model**: 数据模型层（MedicineItem、UserModel等）
- **View**: 视图层（各个页面组件）
- **ViewModel**: 视图模型层（LoginViewModel、RegisterViewModel等）

### 11.4 工厂模式

使用工厂方法创建不同类型的物品：

```typescript
function createItem(type: ItemType, name: string, location: string): MedicineItem {
  // 根据类型创建不同的物品实例
  return new MedicineItem(name, location, getItemTypeIcon(type), 0, type, 0);
}
```

## 12. 应用入口和配置

### 12.1 EntryAbility 类 (应用入口)

**文件位置**: `entry/src/main/ets/entryability/EntryAbility.ets`

**功能描述**: 应用的主入口，继承自 UIAbility

**生命周期方法**:
```typescript
export default class EntryAbility extends UIAbility {
  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void    // 应用创建
  onDestroy(): void                                                       // 应用销毁
  onWindowStageCreate(windowStage: window.WindowStage): void             // 窗口阶段创建
  onWindowStageDestroy(): void                                           // 窗口阶段销毁
  onForeground(): void                                                   // 应用前台
  onBackground(): void                                                   // 应用后台
}
```

**启动配置**: 应用启动时加载 `pages/Login` 页面作为首页

### 12.2 EntryBackupAbility 类 (备份能力)

**文件位置**: `entry/src/main/ets/entrybackupability/EntryBackupAbility.ets`

**功能描述**: 提供应用数据备份和恢复功能

### 12.3 模块配置 (module.json5)

**文件位置**: `entry/src/main/module.json5`

**主要配置**:
```json
{
  "module": {
    "name": "entry",
    "type": "entry",
    "mainElement": "EntryAbility",
    "deviceTypes": ["phone", "tablet", "2in1"],
    "pages": "$profile:main_pages",
    "requestPermissions": [
      {
        "name": "ohos.permission.VIBRATE",
        "reason": "$string:vibrate_permission_reason"
      }
    ]
  }
}
```

### 12.4 页面路由配置 (main_pages.json)

**文件位置**: `entry/src/main/resources/base/profile/main_pages.json`

**页面路由列表**:
```json
{
  "src": [
    "pages/Login",              // 登录页面
    "pages/Index",              // 首页（跳转页）
    "pages/Register",           // 注册页面
    "pages/Home",               // 主页面
    "pages/ForgotPassword",     // 忘记密码页面
    "pages/Settings",           // 设置页面
    "pages/ResetPassword",      // 重置密码页面
    "pages/WebView",            // 网页视图页面
    "pages/DatabaseTest",       // 数据库测试页面
    "pages/AddItem",            // 添加物品页面
    "pages/Notifications"       // 通知页面
  ]
}
```

## 13. 主要业务流程

### 13.1 用户登录流程

1. 应用启动，加载登录页面 (Login.ets)
2. 用户输入用户名和密码，或选择华为账号登录
3. LoginViewModel 验证表单数据
4. 调用 UserService 进行登录验证
5. 登录成功后跳转到主页面 (Home.ets)

### 13.2 物品添加流程

1. 用户在主页面点击添加按钮
2. 打开添加物品弹窗或跳转到添加页面
3. 用户填写物品信息（名称、位置、类型、价格、保质期等）
4. 选择物品类型和存储位置
5. 点击保存，调用 DBManager.addItem() 保存到数据库
6. 更新主页面物品列表和统计信息

### 13.3 物品编辑流程

1. 用户在主页面点击物品卡片
2. 打开物品详情弹窗，显示物品信息
3. 点击编辑按钮进入编辑模式
4. 修改物品信息
5. 点击保存，调用 DBManager.updateItem() 更新数据库
6. 刷新物品列表显示

### 13.4 物品搜索流程

1. 用户在主页面搜索框输入关键词
2. 调用 DBManager.searchItems() 搜索数据库
3. 返回匹配的物品列表
4. 更新页面显示搜索结果

### 13.5 主题切换流程

1. 用户进入设置页面
2. 点击主题切换开关
3. ThemeManager 保存主题设置到 preferences
4. 通知所有注册的组件主题变化
5. 组件接收通知并更新界面主题

### 13.6 过期提醒流程

1. 应用启动时检查所有物品的过期状态
2. 计算物品剩余保质期天数
3. 对于即将过期或已过期的物品生成通知
4. NotificationManager 管理通知列表
5. 在主页面和通知页面显示提醒信息

## 14. 数据存储

### 14.1 关系型数据库 (RDB)

应用使用 HarmonyOS 的关系型数据库存储物品数据：

**数据库配置**:
- 数据库名: `storage_box.db`
- 数据库版本: 4
- 安全级别: S1

**数据表结构**:
```sql
CREATE TABLE IF NOT EXISTS item (
  id INTEGER PRIMARY KEY AUTOINCREMENT,     -- 物品ID
  name TEXT NOT NULL,                       -- 物品名称
  location TEXT NOT NULL,                   -- 存储位置
  image TEXT,                               -- 物品图片
  expiry_days INTEGER DEFAULT 0,            -- 保质期天数
  expiry_unit TEXT DEFAULT 'month',         -- 保质期单位
  item_type INTEGER DEFAULT 6,              -- 物品类型
  price REAL DEFAULT 0.0,                   -- 物品价格
  purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,    -- 购买日期
  production_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,  -- 生产日期
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,      -- 创建时间
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP       -- 更新时间
);
```

### 14.2 首选项存储 (Preferences)

使用 HarmonyOS 的 preferences API 进行用户配置数据的持久化存储：

**存储内容**:
- 用户主题设置
- 用户名信息
- 应用配置选项

**使用方式**:
```typescript
// 获取首选项实例
this.preferences = await preferences.getPreferences(getContext(this), PREFERENCES_NAME);

// 存储数据
await this.preferences?.put(KEY, value);
await this.preferences?.flush();

// 读取数据
const value = await this.preferences?.get(KEY, defaultValue);
```

### 14.3 内存缓存

使用静态变量和单例实例进行内存缓存，提高访问效率：

```typescript
// 全局单例实例缓存
private static instance: UserManager;

// 主题状态缓存
private systemDarkMode: boolean = false;

// 物品数据缓存
private medicineList: MedicineItem[] = [];
```

### 14.4 数据库迁移

支持数据库版本升级和数据迁移：

```typescript
// 数据库迁移方法
private migrateDatabase(fromVersion: number, toVersion: number, callback?: Function): void {
  // 从版本3升级到版本4：添加生产日期字段
  if (fromVersion < 4 && toVersion >= 4) {
    this.rdbStore.executeSql(DBConstants.ADD_PRODUCTION_DATE_COLUMN, null, callback);
  }
}
```

## 15. 接口文档

### 15.1 数据库接口

**DBManager 类主要接口**:

```typescript
// 初始化数据库
initDatabase(callback?: Function): void

// 添加物品
addItem(item: MedicineItem, callback?: Function): void
// 参数: item - 物品对象
// 回调: callback(id: number) - 返回新增物品的ID

// 批量添加物品
batchAddItems(items: MedicineItem[], callback?: Function): void
// 参数: items - 物品对象数组
// 回调: callback(ids: number[]) - 返回新增物品的ID数组

// 更新物品
updateItem(id: number, item: MedicineItem, callback?: Function): void
// 参数: id - 物品ID, item - 更新的物品对象
// 回调: callback(rowsAffected: number) - 返回受影响的行数

// 删除物品
deleteItem(id: number, callback?: Function): void
// 参数: id - 物品ID
// 回调: callback(rowsAffected: number) - 返回受影响的行数

// 获取所有物品
getAllItems(callback: Function): void
// 回调: callback(items: MedicineItem[]) - 返回物品数组

// 搜索物品
searchItems(keyword: string, callback: Function): void
// 参数: keyword - 搜索关键词
// 回调: callback(items: MedicineItem[]) - 返回匹配的物品数组

// 获取物品总数
getItemCount(callback: Function): void
// 回调: callback(count: number) - 返回物品总数

// 计算总价值
calculateTotalValue(callback: Function): void
// 回调: callback(value: number) - 返回物品总价值
```

### 15.2 主题管理接口

**ThemeManager 类主要接口**:

```typescript
// 获取单例实例
static getInstance(): ThemeManager

// 初始化主题管理器
async init(): Promise<void>

// 获取当前主题
async getTheme(): Promise<string>
// 返回值: 'light' | 'dark' | 'auto'

// 设置主题
async setTheme(theme: string): Promise<void>
// 参数: theme - 主题类型 ('light' | 'dark' | 'auto')

// 判断当前是否为深色模式
async isDarkMode(): Promise<boolean>
// 返回值: boolean - 是否为深色模式

// 注册主题变化回调
registerThemeChangeCallback(callback: (isDark: boolean) => void): void
// 参数: callback - 主题变化时的回调函数

// 移除主题变化回调
unregisterThemeChangeCallback(callback: (isDark: boolean) => void): void
// 参数: callback - 要移除的回调函数
```

### 15.3 用户管理接口

**UserManager 类主要接口**:

```typescript
// 获取单例实例
static getInstance(): UserManager

// 初始化用户管理器
async init(): Promise<void>

// 获取用户名
async getUsername(): Promise<string>
// 返回值: string - 用户名

// 设置用户名
async setUsername(username: string): Promise<void>
// 参数: username - 新的用户名

// 注册用户名变化回调
registerUsernameChangeCallback(callback: (username: string) => void): void
// 参数: callback - 用户名变化时的回调函数

// 移除用户名变化回调
unregisterUsernameChangeCallback(callback: (username: string) => void): void
// 参数: callback - 要移除的回调函数
```

### 15.4 通知管理接口

**NotificationManager 类主要接口**:

```typescript
// 获取单例实例
static getInstance(): NotificationManager

// 添加通知
addNotification(item: Item): void
// 参数: item - 物品对象

// 移除通知
removeNotification(id: string): void
// 参数: id - 通知ID

// 获取所有通知
getNotifications(): Notification[]
// 返回值: Notification[] - 通知数组

// 获取未读通知数量
getUnreadCount(): number
// 返回值: number - 未读通知数量

// 标记为已读
markAsRead(id: string): void
// 参数: id - 通知ID

// 清空所有通知
clearAllNotifications(): void
```

## 16. 界面设计

### 16.1 配色方案

应用支持亮色和暗色两套配色方案：

**亮色主题**：
- 主色调：蓝色 (#3478F6)
- 背景色：白色 (#FFFFFF)
- 卡片背景：白色 (#FFFFFF)
- 文本颜色：深灰 (#333333)
- 分割线：浅灰 (#E0E0E0)

**暗色主题**：
- 主色调：蓝色 (#3478F6)
- 背景色：深灰 (#121212)
- 卡片背景：中灰 (#1E1E1E)
- 文本颜色：浅灰 (#E0E0E0)
- 分割线：深灰 (#333333)

### 16.2 交互设计

- **滑动手势**：支持上滑打开弹窗、侧滑打开菜单等手势操作
- **动画效果**：弹窗淡入淡出、侧边菜单滑入滑出等过渡动画
- **反馈机制**：按钮点击效果、保存成功提示等用户反馈
- **主题适应**：所有UI元素根据当前主题自动调整颜色和样式
- **响应式布局**：支持不同屏幕尺寸的设备（手机、平板、2in1设备）

## 17. 性能优化

### 17.1 已实施的优化

- **懒加载**：物品列表采用懒加载方式，减少初始加载时间
- **状态管理**：合理使用 ArkTS 的状态管理，避免不必要的重渲染
- **动画性能**：使用硬件加速的动画效果，保证流畅性
- **回调管理**：确保组件销毁时取消注册回调，防止内存泄漏
- **单例模式**：使用单例模式减少对象创建，优化内存使用
- **数据库连接池**：复用数据库连接，减少连接开销
- **批量操作**：支持批量插入和更新，提高数据库操作效率

### 17.2 未来优化计划

- **数据缓存**：实现本地数据缓存，提高加载速度
- **图片优化**：添加图片压缩和缓存机制
- **离线支持**：实现完整的离线使用功能
- **代码分割**：按需加载代码，减少初始加载时间
- **虚拟列表**：对于大量数据的列表使用虚拟滚动技术

## 18. 已知问题与解决方案

### 18.1 已知问题

- **华为账号登录**：目前华为账号登录功能为模拟实现，未集成实际的华为账号服务
- **语音识别功能**：语音输入功能尚未完全实现，需要集成语音识别服务
- **位置管理**：位置词条的编辑和管理需要进一步完善
- **过期提醒**：过期提醒机制需要增加定时通知功能
- **系统主题监听**：当前使用定时检查方式监听系统主题变化，不够实时
- **数据同步**：缺少云端数据同步功能

### 18.2 解决方案

- 集成华为账号服务 SDK，实现真实的华为账号登录功能
- 集成华为语音识别服务，提高语音输入的准确率
- 优化位置管理界面，增加批量编辑和导入导出功能
- 实现基于通知中心的定时提醒功能
- 探索更高效的系统主题变化监听方法，提高响应速度
- 开发云端数据同步功能，支持多设备数据同步

## 19. 未来规划

### 19.1 短期计划 (1-3个月)

- **华为账号集成**：完成华为账号服务的真实集成
- **语音识别实现**：集成华为语音识别服务
- **物品图片功能**：增加物品详情页的图片上传和显示功能
- **数据导出备份**：实现数据导出和备份功能
- **通知系统完善**：实现基于系统通知的过期提醒功能
- **位置管理优化**：完善位置管理界面和功能

### 19.2 中期计划 (3-6个月)

- **扫码录入功能**：添加条形码/二维码扫描录入物品功能
- **数据分析功能**：开发物品使用和过期趋势分析
- **自定义主题**：增加用户自定义主题功能
- **批量操作优化**：完善批量添加、编辑、删除功能
- **搜索功能增强**：支持高级搜索和筛选功能
- **用户体验优化**：改进界面交互和动画效果

### 19.3 长期计划 (6个月以上)

- **云同步功能**：实现云端数据同步，支持多设备数据同步
- **家庭共享功能**：支持多用户协作管理物品
- **AI智能推荐**：基于用户习惯的智能物品管理建议
- **物联网集成**：与智能家居设备集成，自动感知物品状态
- **社区功能**：用户间的物品管理经验分享
- **多语言支持**：支持国际化，提供多语言版本

## 20. 开发与调试指南

### 20.1 环境配置

**必需环境**:
- DevEco Studio 4.0 或更高版本
- HarmonyOS SDK API 9 或更高版本
- Node.js 14.0 或更高版本

**推荐配置**:
- 内存：8GB 或更高
- 存储：至少 10GB 可用空间
- 操作系统：Windows 10/11, macOS 10.15+, Ubuntu 18.04+

### 20.2 项目构建

```bash
# 克隆项目
git clone <repository-url>
cd Storage-and-boxes

# 安装依赖
npm install

# 构建应用
hvigor build

# 运行应用（需要连接设备或模拟器）
hvigor run
```

### 20.3 调试技巧

**日志调试**:
- 使用 `Logger.info()` 输出关键日志信息
- 在 DevEco Studio 中查看 HiLog 输出
- 设置不同的日志级别进行分类调试

**界面调试**:
- 使用 DevEco Studio 的布局检查器查看组件层次
- 测试不同屏幕尺寸和方向的适配
- 在不同主题模式下测试界面显示

**数据库调试**:
- 使用 DatabaseTest 页面测试数据库操作
- 检查数据库文件和表结构
- 验证数据的增删改查功能

**性能调试**:
- 使用 DevEco Studio 的性能分析工具
- 监控内存使用和 CPU 占用
- 检查组件渲染性能

### 20.4 常见问题解决

**构建问题**:
- 检查 SDK 版本是否匹配
- 清理构建缓存：`hvigor clean`
- 重新安装依赖：`rm -rf node_modules && npm install`

**运行时问题**:
- 检查设备连接和权限设置
- 查看应用日志输出
- 验证数据库初始化是否成功

**界面问题**:
- 检查主题管理器初始化
- 验证组件状态更新机制
- 确认资源文件路径正确

## 21. 测试指南

### 21.1 单元测试

**测试文件位置**: `entry/src/test/`

**主要测试内容**:
- 数据模型的创建和转换
- 数据库操作的正确性
- 业务逻辑的验证
- 工具类方法的测试

**运行测试**:
```bash
# 运行所有测试
hvigor test

# 运行特定测试文件
hvigor test --testFile LocalUnit.test.ets
```

### 21.2 集成测试

**测试页面**: DatabaseTest.ets

**测试功能**:
- 数据库连接和初始化
- 数据的增删改查操作
- 搜索功能验证
- 统计功能测试

### 21.3 用户界面测试

**测试内容**:
- 页面跳转和导航
- 表单输入和验证
- 主题切换功能
- 响应式布局适配

**测试设备**:
- 不同尺寸的手机设备
- 平板设备
- 2in1 设备

## 22. 部署指南

### 22.1 应用打包

```bash
# 生成 HAP 包
hvigor assembleHap

# 生成 APP 包
hvigor assembleApp
```

### 22.2 签名配置

1. 在 DevEco Studio 中配置签名证书
2. 设置应用包名和版本信息
3. 配置发布证书和 Profile 文件

### 22.3 应用发布

1. **华为应用市场**：
   - 注册开发者账号
   - 上传应用包和相关资料
   - 等待审核通过

2. **企业内部分发**：
   - 生成企业签名的 HAP 包
   - 通过内部渠道分发

## 23. 贡献指南

欢迎贡献代码或提出改进建议。请遵循以下步骤：

### 23.1 代码贡献流程

1. Fork 仓库到个人账号
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 编写代码并添加相应测试
4. 确保代码符合项目规范
5. 提交更改 (`git commit -m 'Add some amazing feature'`)
6. 推送到分支 (`git push origin feature/amazing-feature`)
7. 创建 Pull Request

### 23.2 代码规范

- 遵循 ArkTS 编码规范
- 添加必要的注释和文档
- 确保新功能有对应的测试用例
- 保持代码风格一致

### 23.3 问题反馈

- 使用 GitHub Issues 报告 Bug
- 提供详细的问题描述和复现步骤
- 包含设备信息和应用版本

## 24. 版权信息

Copyright © 2024 物匣开发团队. All rights reserved.

本项目基于 MIT 许可证开源，详见 LICENSE 文件。

## 25. 联系方式

- **项目主页**: [GitHub Repository]
- **问题反馈**: [GitHub Issues]
- **开发团队**: 物匣开发团队
- **技术支持**: 通过 GitHub Issues 或邮件联系

---

**文档版本**: v1.0
**最后更新**: 2024年12月
**适用版本**: Storage and Boxes v1.0+