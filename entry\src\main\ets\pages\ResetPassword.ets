/**
 * 修改密码页面设计
 */

import { ForgotPasswordViewModel } from '../viewmodel/ForgotPasswordViewModel';
import promptAction from '@ohos.promptAction';
import router from '@ohos.router';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';

@Entry
@Component
struct ResetPassword {
  @State forgotPasswordViewModel: ForgotPasswordViewModel = new ForgotPasswordViewModel();
  @State oldPassword: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State isAgreeProtocol: boolean = false;
  @State isLoading: boolean = false;
  @State showOldPassword: boolean = false;
  @State showNewPassword: boolean = false;
  @State showConfirmPassword: boolean = false;
  
  // 主题相关状态
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  aboutToAppear() {
    console.info('ResetPassword页面 - aboutToAppear');
    try {
      // 获取路由参数
      const params = router.getParams() as Record<string, string>;
      console.info('ResetPassword页面 - 路由参数:', JSON.stringify(params));
      
      // 初始化主题管理器
      this.themeManager.init().then(() => {
        // 加载当前主题设置
        this.loadThemeSettings();
      }).catch((err: Error) => {
        console.error('主题管理器初始化失败:', err.message);
      });
      
      // 注册主题变化回调
      this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
    } catch (error) {
      console.error('ResetPassword页面初始化错误:', error instanceof Error ? error.message : String(error));
    }
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      const isDark = await this.themeManager.isDarkMode();
      this.updateTheme(isDark);
    } catch (err) {
      console.error('加载主题设置失败', err instanceof Error ? err.message : String(err));
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('ResetPassword页面主题更新', isDark ? '深色模式' : '浅色模式');
  }

  /**
   * 返回设置页面
   */
  backToSettings() {
    router.back();
  }

  build() {
    Column() {
      // 顶部欢迎文本
      Column() {
        Text('HELLO,')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 20, bottom: 15 })
          .alignSelf(ItemAlign.Start)
          .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
        
        Text('修改您的密码')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
          .alignSelf(ItemAlign.Start)
          .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
      }
      .width('70%')
      
      // 中间图片 - 植物盒子
      Image($r('app.media.plant_box'))
        .width('auto')
        .height(180)
        .margin({ bottom: 20 })

      // 密码修改表单
      Column() {
        // 旧密码输入框
        Column() {
          TextInput({ placeholder: '请输入当前密码' })
            .height(50)
            .width('100%')
            .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
            .borderRadius(24)
            .padding({ left: 16, right: 16 })
            .margin({ bottom: 10 })
            .type(this.showOldPassword ? InputType.Normal : InputType.Password)
            .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
            .placeholderColor(this.isDarkMode ? '#999999' : '#999999')
            .onChange((value: string) => {
              this.oldPassword = value;
            })
        }
        .width('90%')
        
        // 新密码输入框
        TextInput({ placeholder: '请输入新密码' })
          .type(this.showNewPassword ? InputType.Normal : InputType.Password)
          .height(50)
          .width('90%')
          .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
          .placeholderColor(this.isDarkMode ? '#999999' : '#999999')
          .onChange((value: string) => {
            this.newPassword = value;
            this.forgotPasswordViewModel.setNewPassword(value);
          })
        
        // 确认新密码输入框
        TextInput({ placeholder: '请确认新密码' })
          .type(this.showConfirmPassword ? InputType.Normal : InputType.Password)
          .height(50)
          .width('90%')
          .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
          .placeholderColor(this.isDarkMode ? '#999999' : '#999999')
          .onChange((value: string) => {
            this.confirmPassword = value;
            this.forgotPasswordViewModel.setConfirmPassword(value);
          })
        
        // 修改密码按钮
        Button('确认修改')
          .width('90%')
          .height(50)
          .borderRadius(24)
          .backgroundColor('#A9BE82')
          .fontColor(Color.White)
          .fontSize(16)
          .enabled(!this.isLoading && this.isAgreeProtocol)
          .opacity(this.isAgreeProtocol ? 1 : 0.5)
          .onClick(() => this.handleResetPassword())
          .margin({ bottom: 10 })
          
        // 返回设置页面
        Row() {
          Text('返回设置')
            .fontSize(14)
            .fontColor('#666666')
            .onClick(() => this.backToSettings())
        }
        .width('90%')
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 10 })
      }
      .width('90%')
      
      // 底部协议和技术支持
      Column() {
        // 底部协议
        Row() {
          Checkbox()
            .select(this.isAgreeProtocol)
            .onChange((value: boolean) => {
              this.isAgreeProtocol = value;
            })
          
          Text('我已阅读并同意《用户协议》和《隐私政策》')
            .fontSize(12)
            .opacity(0.6)
            .fontColor(this.isDarkMode ? '#CCCCCC' : '#666666')
        }
        .width('100%')
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 1})
      }
      .width('100%')
      .position({ bottom: 10 })
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.isDarkMode ? '#121212' : Color.White)
  }
  
  /**
   * 处理重置密码逻辑
   */
  handleResetPassword() {
    // 密码验证
    if (!this.oldPassword || this.oldPassword.length < 6) {
      promptAction.showToast({ message: '请输入当前密码' });
      return;
    }
    
    if (!this.newPassword || this.newPassword.length < 6) {
      promptAction.showToast({ message: '请输入至少6位的新密码' });
      return;
    }
    
    if (this.newPassword !== this.confirmPassword) {
      promptAction.showToast({ message: '两次输入的密码不一致' });
      return;
    }
    
    if (!this.isAgreeProtocol) {
      promptAction.showToast({ message: '请阅读并同意用户协议' });
      return;
    }
    
    this.isLoading = true;
    
    // 模拟修改密码流程
    setTimeout(() => {
      promptAction.showToast({ message: '密码修改成功' });
      this.isLoading = false;
      
      // 延迟返回上一页
      setTimeout(() => {
        router.back();
      }, 1000);
    }, 1500);
  }
} 