import { ThemeConstants, ThemeColors } from '../constants/ThemeConstants';
import { ThemeManager } from '../utils/ThemeManager';

/**
 * 主题提供者组件
 * 作为应用的根组件，提供主题上下文
 */
@Component
export struct ThemeProvider {
  @State currentTheme: string = ThemeConstants.THEME_LIGHT;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  private themeManager: ThemeManager = ThemeManager.getInstance();
  @BuilderParam content: () => void;
  
  aboutToAppear() {
    // 加载主题设置
    this.loadThemeSettings();
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      this.currentTheme = await this.themeManager.getTheme();
      this.colors = this.themeManager.getThemeColors(this.currentTheme);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  build() {
    Column() {
      // 在此包裹子组件，并将主题上下文传递给子组件
      this.content();
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
  }
} 