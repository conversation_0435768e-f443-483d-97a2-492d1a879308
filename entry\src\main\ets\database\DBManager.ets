import { DBHelper } from './DBHelper';
import { MedicineItem } from '../model/MedicineItem';
import { ItemType } from '../model/ItemType';
import Logger from '../common/Logger';
import relationalStore from '@ohos.data.relationalStore';

const TAG = 'DBManager';

/**
 * 数据库管理器，单例模式
 */
export class DBManager {
  private static instance: DBManager;
  private dbHelper: DBHelper = new DBHelper();

  // 样例数据
  private sampleItems: MedicineItem[] = [
    new MedicineItem('三金西瓜霜片', '客厅/电视柜/底药箱', $r('app.media.add'), 0, ItemType.MEDICINE, 68.9),
    new MedicineItem('感冒灵颗粒', '卧室/床头柜/药盒', $r('app.media.add'), 0, ItemType.MEDICINE, 35.5),
    new MedicineItem('西红柿', '厨房/冰箱/蔬菜层', $r('app.media.add'), 3, ItemType.VEGETABLE, 12.8),
    new MedicineItem('黄瓜', '厨房/冰箱/蔬菜层', $r('app.media.add'), 3, ItemType.VEGETABLE, 8.5),
    new MedicineItem('牛肉', '厨房/冰箱/冷冻层', $r('app.media.add'), 7, ItemType.MEAT, 89.9),
    new MedicineItem('鸡蛋', '厨房/冰箱/蛋托', $r('app.media.add'), 7, ItemType.EGG, 28.8),
    new MedicineItem('牛奶', '厨房/冰箱/门层', $r('app.media.add'), 7, ItemType.MILK, 42.5),
    new MedicineItem('充电宝', '书房/抽屉/电子产品', $r('app.media.add'), 0, ItemType.ELECTRONIC, 199.0),
    new MedicineItem('笔记本', '客厅/茶几/抽屉', $r('app.media.add'), 0, ItemType.GENERAL, 25.0),
  ];

  /**
   * 获取实例
   */
  static getInstance(): DBManager {
    if (!DBManager.instance) {
      DBManager.instance = new DBManager();
    }
    return DBManager.instance;
  }

  /**
   * 初始化数据库
   * @param callback 回调函数
   */
  initDatabase(callback?: Function): void {
    this.dbHelper.getRdbStore((store: relationalStore.RdbStore) => {
      Logger.info(TAG, 'Database initialized successfully');
      callback && callback();
    });
  }

  /**
   * 导入样例数据（如果数据库为空）
   * @param callback 回调函数
   */
  importSampleDataIfNeeded(callback: () => void): void {
    // 检查数据库中是否已有数据
    this.getAllItems((items: MedicineItem[]) => {
      if (items.length === 0) {
        // 创建两个预设物品
        const presetItems: MedicineItem[] = [
          new MedicineItem('感冒灵颗粒', '卧室/床头柜/药盒', $r('app.media.add'), 0, ItemType.MEDICINE, 35.5),
          new MedicineItem('牛奶', '厨房/冰箱/门层', $r('app.media.add'), 7, ItemType.MILK, 42.5)
        ];

        // 批量插入预设物品
        this.batchAddItems(presetItems, () => {
          console.info('预设数据导入成功');
          callback();
        });
      } else {
        callback();
      }
    });
  }

  /**
   * 添加物品
   * @param item 物品数据
   * @param callback 回调函数，返回物品ID
   */
  addItem(item: MedicineItem, callback?: Function): void {
    this.dbHelper.insertItem(item, (id: number) => {
      Logger.info(TAG, `Item added successfully, id: ${id}`);
      callback && callback(id);
    });
  }

  /**
   * 批量添加物品
   * @param items 物品数据数组
   * @param callback 回调函数，返回物品ID数组
   */
  batchAddItems(items: MedicineItem[], callback?: Function): void {
    this.dbHelper.batchInsertItems(items, (ids: number[]) => {
      Logger.info(TAG, `Items added successfully, count: ${ids.length}`);
      callback && callback(ids);
    });
  }

  /**
   * 更新物品
   * @param id 物品ID
   * @param item 物品数据
   * @param callback 回调函数，返回更新结果
   */
  updateItem(id: number, item: MedicineItem, callback?: Function): void {
    this.dbHelper.updateItem(id, item, (rows: number) => {
      const success = rows > 0;
      Logger.info(TAG, `Item updated successfully: ${success}`);
      callback && callback(success);
    });
  }

  /**
   * 删除物品
   * @param id 物品ID
   * @param callback 回调函数，返回删除结果
   */
  deleteItem(id: number, callback?: Function): void {
    this.dbHelper.deleteItem(id, (rows: number) => {
      const success = rows > 0;
      Logger.info(TAG, `Item deleted successfully: ${success}`);
      callback && callback(success);
    });
  }

  /**
   * 获取所有物品
   * @param callback 回调函数，返回物品数据数组
   */
  getAllItems(callback: Function): void {
    this.dbHelper.queryAllItems(callback);
  }

  /**
   * 按类型获取物品
   * @param itemType 物品类型
   * @param callback 回调函数，返回物品数据数组
   */
  getItemsByType(itemType: ItemType, callback: Function): void {
    this.dbHelper.queryItemsByType(itemType, callback);
  }

  /**
   * 搜索物品
   * @param keyword 搜索关键词
   * @param callback 回调函数，返回物品数据数组
   */
  searchItems(keyword: string, callback: Function): void {
    this.dbHelper.searchItemsByName(keyword, callback);
  }

  /**
   * 计算物品总数
   * @param callback 回调函数，返回物品总数
   */
  getItemCount(callback: Function): void {
    this.getAllItems((items: MedicineItem[]) => {
      callback(items.length);
    });
  }

  /**
   * 计算物品总价值
   * @param callback 回调函数，返回物品总价值
   */
  calculateTotalValue(callback: Function): void {
    this.getAllItems((items: MedicineItem[]) => {
      const totalValue = items.reduce((total, item) => total + item.price, 0);
      callback(totalValue);
    });
  }
} 