import { LoginViewModel } from '../viewmodel/LoginViewModel';
import promptAction from '@ohos.promptAction';
import account from '@ohos.account.appAccount';
import router from '@ohos.router';
// 导入华为账号服务相关SDK

@Entry
@Component
struct Login {
  @State loginViewModel: LoginViewModel = new LoginViewModel();
  @State username: string = '';
  @State password: string = '';
  @State isAgreeProtocol: boolean = false;
  @State isLoading: boolean = false;
  @State showPassword: boolean = false;

  aboutToAppear() {
    // 初始化视图模型
  }

  build() {
    Column() {
      // 顶部欢迎文本
      Column() {
        Text('HELLO,')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 20, bottom: 15 })
          .alignSelf(ItemAlign.Start)
        
        Text('欢迎使用物匣')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 60 })
          .alignSelf(ItemAlign.Start)
      }
      .width('70%')
      
      // 中间图片 - 植物盒子
      Image($r('app.media.plant_box'))
        .width('auto')
        .height(180)
        .margin({ bottom: 20 })

      // 登录表单
      Column() {
        // 用户名输入框
        TextInput({ placeholder: '请输入账号' })
          .height(50)
          .width('90%')
          .backgroundColor('#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .onChange((value: string) => {
            this.username = value;
            this.loginViewModel.setUsername(value);
          })
        
        // 密码输入框
        TextInput({ placeholder: '请输入密码' })
          .type(this.showPassword ? InputType.Normal : InputType.Password)
          .height(50)
          .width('90%')
          .backgroundColor('#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .onChange((value: string) => {
            this.password = value;
            this.loginViewModel.setPassword(value);
          })
        
        // 登录按钮
        Button('登 录')
          .width('90%')
          .height(50)
          .borderRadius(24)
          .backgroundColor('#A9BE82')
          .fontColor(Color.White)
          .fontSize(16)
          .enabled(!this.isLoading && this.isAgreeProtocol)
          .opacity(this.isAgreeProtocol ? 1 : 0.5)
          .onClick(() => this.handleLogin())
          .margin({ bottom: 10 })
        
        // 华为一键登录按钮
        Button() {
          Row() {
            Image($r('app.media.huawei_icon'))
              .width(24)
              .height(24)
              .margin({ right: 8 })
            Text('华为账号一键登录')
              .fontSize(16)
              .fontColor('#000000')
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
        }
        .width('90%')
        .height(50)
        .borderRadius(24)
        .backgroundColor('#FFFFFF')
        .border({ width: 1, color: '#df031c' })
        .enabled(!this.isLoading && this.isAgreeProtocol)
        .opacity(this.isAgreeProtocol ? 1 : 0.5)
        .onClick(() => this.handleHuaweiLogin())
        .margin({ bottom: 10 })
        
        // 立即注册和忘记密码文本链接
        Row() {
          Text('立即注册')
            .fontSize(14)
            .fontColor('#666666')
            .onClick(() => this.handleRegister())
          
          Text('忘记密码')
            .fontSize(14)
            .fontColor('#666666')
            .onClick(() => this.handleForgetPassword())
        }
        .width('90%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ bottom: 10 })
      }
      .width('90%')
      
      // 底部协议和技术支持
      Column() {
        // 底部协议
        Column() {
          // 第一行
          Row() {
            Checkbox()
              .select(this.isAgreeProtocol)
              .onChange((value: boolean) => {
                this.isAgreeProtocol = value;
                this.loginViewModel.setAgreeProtocol(value);
              })
              .margin({ right: 5 })
              
            Text() {
              Span('我已阅读并同意')
                .fontColor('#666666')
              Span('《用户协议》')
                .onClick(() => this.openUserAgreement())
                .fontColor('#0972d3')
                .fontWeight(FontWeight.Medium)
              Span('、')
                .fontColor('#666666')
              Span('《隐私政策》')
                .onClick(() => this.openPrivacyPolicy())
                .fontColor('#0972d3')
                .fontWeight(FontWeight.Medium)
            }
            .fontSize(13)
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          
          // 第二行
          Row() {
            Text() {
              Span('和')
                .fontColor('#666666')
              Span('《华为账号用户认证协议》')
                .onClick(() => this.openHuaweiAgreement())
                .fontColor('#0972d3')
                .fontWeight(FontWeight.Medium)
            }
            .fontSize(13)
            .margin({ left: 29 }) // 与第一行文字对齐
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ top: 5 })
        }
        .width('90%')
        .margin({ bottom: 5 })
        .padding({ top: 5, bottom: 5 })

      }
      .width('100%')
      .position({ bottom: 10 })
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.White)
    //.padding({ left: 16, right: 16 })
  }
  
  /**
   * 处理登录逻辑
   */
  handleLogin() {
    this.isLoading = true;
    this.loginViewModel.setLoadingState(true);
    
    this.loginViewModel.login().then(result => {
      if (result.isSuccess) {
         promptAction.showToast({ message: '登录成功' });
        // 登录成功后跳转到Home页面
        router.pushUrl({
          url: 'pages/Home'
        }).catch((err: Error) => {
          console.error(`跳转到首页失败: ${err.message}`);
          promptAction.showToast({ message: `跳转到首页失败: ${err.message}` });
        });
      } else {
        promptAction.showToast({ message: result.message });
      }
    }).catch((error: Error) => {
      promptAction.showToast({ message: '登录失败: ' + error.message });
    }).finally(() => {
      this.isLoading = false;
      this.loginViewModel.setLoadingState(false);
    });
  }
  
  /**
   * 处理华为一键登录逻辑
   */
  handleHuaweiLogin() {
    this.isLoading = true;
    this.loginViewModel.setLoadingState(true);
    
    this.loginViewModel.huaweiLogin().then(result => {
      if (result.isSuccess) {
        promptAction.showToast({ message: result.message });
        // 登录成功后跳转到Home页面
        router.pushUrl({
          url: 'pages/Home'
        }).catch((err: Error) => {
          console.error(`跳转到首页失败: ${err.message}`);
          promptAction.showToast({ message: `跳转到首页失败: ${err.message}` });
        });
      } else {
        promptAction.showToast({ message: result.message });
      }
    }).catch((error: Error) => {
      promptAction.showToast({ message: '华为账号登录失败: ' + error.message });
    }).finally(() => {
      this.isLoading = false;
      this.loginViewModel.setLoadingState(false);
    });
  }
  
  /**
   * 处理立即注册逻辑
   */
  handleRegister() {
    this.loginViewModel.register().then(result => {
      promptAction.showToast({ message: result.message });
      // 跳转到注册页面
      router.pushUrl({
        url: 'pages/Register'
      }).catch((err: Error) => {
        console.error(`跳转到注册页面失败: ${err.message}`);
        promptAction.showToast({ message: `跳转到注册页面失败: ${err.message}` });
      });
    }).catch((error: Error) => {
      promptAction.showToast({ message: '注册失败: ' + error.message });
    });
  }
  
  /**
   * 处理忘记密码逻辑
   */
  handleForgetPassword() {
    this.loginViewModel.forgetPassword().then(result => {
      // 跳转到忘记密码页面
      router.pushUrl({
        url: 'pages/ForgotPassword'
      }).catch((err: Error) => {
        console.error(`跳转到忘记密码页面失败: ${err.message}`);
        promptAction.showToast({ message: `跳转到忘记密码页面失败: ${err.message}` });
      });
    }).catch((error: Error) => {
      promptAction.showToast({ message: '操作失败: ' + error.message });
    });
  }

  /**
   * 打开用户协议
   */
  openUserAgreement() {
    // 跳转到WebView页面打开用户协议
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '用户协议',
        url: 'https://example.com/user_agreement.html'
      }
    }).catch((err: Error) => {
      console.error(`打开用户协议失败: ${err.message}`);
      promptAction.showToast({ message: `打开用户协议失败: ${err.message}` });
    });
  }

  /**
   * 打开隐私政策
   */
  openPrivacyPolicy() {
    // 跳转到WebView页面打开隐私政策
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '隐私政策',
        url: 'https://example.com/privacy_policy.html'
      }
    }).catch((err: Error) => {
      console.error(`打开隐私政策失败: ${err.message}`);
      promptAction.showToast({ message: `打开隐私政策失败: ${err.message}` });
    });
  }

  /**
   * 打开华为账号用户认证协议
   */
  openHuaweiAgreement() {
    // 直接使用router跳转到WebView页面，传递协议URL
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '华为账号用户认证协议',
        url: 'https://privacy.consumer.huawei.com/legal/id/authentication-terms.htm?code=CN&language=zh-CN'
      }
    }).catch((err: Error) => {
      console.error(`打开华为账号用户认证协议失败: ${err.message}`);
      promptAction.showToast({ message: `打开华为账号用户认证协议失败: ${err.message}` });
    });
  }
}