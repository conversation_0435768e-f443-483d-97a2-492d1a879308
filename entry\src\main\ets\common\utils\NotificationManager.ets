/**
 * 通知管理器
 * 管理应用内的通知状态
 */

// 创建一个物品接口来替代any类型
export interface Item {
  name: string;
  location: string;
  expireDay: number;
  id?: number;
}

// 通知类型枚举
export enum NotificationType {
  SYSTEM = 0,    // 系统通知
  EXPIRY = 1,    // 过期提醒
  UPDATE = 2     // 更新通知
}

// 过期状态枚举
export enum ExpiryStatus {
  EXPIRED = 0,      // 已过期
  EXPIRING_SOON = 1, // 即将过期（7天内）
  VALID = 2         // 有效期内
}

// 通知项类型
export class NotificationItem {
  id: number;             // 通知ID
  title: string;          // 通知标题
  content: string;        // 通知内容简介
  time: string;           // 通知时间
  isRead: boolean;        // 是否已读
  type: NotificationType; // 通知类型
  relatedId?: number;     // 关联ID（如物品ID）
  detailContent?: string; // 详细内容
  
  constructor(id: number, title: string, content: string, time: string, isRead: boolean, 
              type: NotificationType = NotificationType.SYSTEM, relatedId?: number, detailContent?: string) {
    this.id = id;
    this.title = title;
    this.content = content;
    this.time = time;
    this.isRead = isRead;
    this.type = type;
    this.relatedId = relatedId;
    this.detailContent = detailContent;
  }
}

export class NotificationManager {
  private static instance: NotificationManager;
  private unreadCount: number = 0; // 初始值改为0，由updateUnreadCount()设置正确值
  private callbackMap: Map<string, (count: number) => void> = new Map();
  private notifications: NotificationItem[] = []; // 存储通知列表
  private hasInitialized: boolean = false; // 是否已初始化通知数据
  private nextId: number = 3; // 下一个通知ID（调整为3，因为移除了预设过期提醒）
  
  // 新增：用于跟踪已通知的过期物品
  private lastNotifiedExpiredIds: Set<number> = new Set(); // 记录已通知的过期物品ID
  private lastNotifiedExpiringSoonIds: Set<number> = new Set(); // 记录已通知的即将过期物品ID
  
  /**
   * 私有构造函数，立即初始化通知数据
   */
  private constructor() {
    // 立即加载示例通知数据，确保数据一致性
    this.loadSampleNotifications();
  }
  
  /**
   * 获取单例实例
   */
  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }
  
  /**
   * 获取未读通知数量
   */
  public getUnreadCount(): number {
    return this.unreadCount;
  }
  
  /**
   * 设置未读通知数量
   * @param count 未读数量
   */
  public setUnreadCount(count: number): void {
    this.unreadCount = count;
    
    // 通知所有回调
    this.callbackMap.forEach((callback) => {
      callback(count);
    });
  }
  
  /**
   * 注册未读数量变化回调
   * @param key 回调标识
   * @param callback 回调函数
   */
  public registerUnreadCountCallback(key: string, callback: (count: number) => void): void {
    this.callbackMap.set(key, callback);
  }
  
  /**
   * 注销未读数量变化回调
   * @param key 回调标识
   */
  public unregisterUnreadCountCallback(key: string): void {
    this.callbackMap.delete(key);
  }
  
  /**
   * 获取通知列表
   * @returns 通知列表
   */
  public getNotifications(): NotificationItem[] {
    return this.notifications;
  }
  
  /**
   * 标记通知为已读
   * @param id 通知ID
   */
  public markAsRead(id: number): void {
    const index = this.notifications.findIndex(item => item.id === id);
    if (index !== -1) {
      this.notifications[index].isRead = true;
      this.updateUnreadCount();
    }
  }
  
  /**
   * 标记通知为未读
   * @param id 通知ID
   */
  public markAsUnread(id: number): void {
    const index = this.notifications.findIndex(item => item.id === id);
    if (index !== -1) {
      this.notifications[index].isRead = false;
      this.updateUnreadCount();
    }
  }
  
  /**
   * 标记所有通知为已读
   */
  public markAllAsRead(): void {
    let hasUnread = false;
    this.notifications.forEach(item => {
      if (!item.isRead) {
        item.isRead = true;
        hasUnread = true;
      }
    });
    
    if (hasUnread) {
      this.updateUnreadCount();
    }
  }
  
  /**
   * 删除单条通知
   * @param id 通知ID
   * @returns 是否删除成功
   */
  public deleteNotification(id: number): boolean {
    const index = this.notifications.findIndex(item => item.id === id);
    if (index !== -1) {
      this.notifications.splice(index, 1);
      this.updateUnreadCount();
      return true;
    }
    return false;
  }
  
  /**
   * 清空所有通知
   */
  public clearAllNotifications(): void {
    this.notifications = [];
    this.updateUnreadCount();
  }
  
  /**
   * 添加新通知
   * @param title 通知标题
   * @param content 通知内容
   * @param type 通知类型
   * @param relatedId 关联ID
   * @param detailContent 详细内容
   * @returns 新通知的ID
   */
  public addNotification(title: string, content: string, type: NotificationType = NotificationType.SYSTEM, 
                        relatedId?: number, detailContent?: string): number {
    // 获取当前时间
    const now = new Date();
    const timeString = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;
    
    // 创建新通知
    const newNotification = new NotificationItem(
      this.nextId++,
      title,
      content,
      timeString,
      false,
      type,
      relatedId,
      detailContent
    );
    
    // 添加到通知列表的开头（最新的通知显示在前面）
    this.notifications.unshift(newNotification);
    
    // 更新未读数量
    this.updateUnreadCount();
    
    return newNotification.id;
  }
  
  /**
   * 加载示例通知数据
   */
  private loadSampleNotifications(): void {
    this.notifications = [
      new NotificationItem(1, '系统通知', '欢迎使用物品管家！', '2024-05-01 10:20', false, 
                          NotificationType.SYSTEM, undefined, '欢迎使用物品管家！这是一款帮助您管理家中物品的应用程序。您可以添加、编辑和删除物品，查看物品详情，以及接收物品过期提醒。'),
      new NotificationItem(2, '功能更新', '新版本V1.2.0已发布，新增语音识别功能', '2024-05-03 09:18', false, 
                          NotificationType.UPDATE, undefined, '新版本V1.2.0已发布，主要更新内容：\n1. 新增语音识别功能，可以通过语音添加物品\n2. 优化用户界面，提升用户体验\n3. 修复已知问题，提高应用稳定性')
    ];
    this.updateUnreadCount();
  }
  
  /**
   * 更新未读数量
   */
  private updateUnreadCount(): void {
    this.unreadCount = this.notifications.filter(item => !item.isRead).length;
    
    // 通知所有回调
    this.callbackMap.forEach(callback => {
      callback(this.unreadCount);
    });
  }
  
  /**
   * 检查过期物品并生成通知
   * @param items 物品列表
   */
  public checkExpiryAndNotify(items: Item[]): void {
    if (!items || items.length === 0) {
      return;
    }
    
    // 过滤出已过期和即将过期的物品
    const expiredItems: Item[] = items.filter(item => this.getExpiryStatus(item.expireDay) === ExpiryStatus.EXPIRED);
    const expiringItems: Item[] = items.filter(item => this.getExpiryStatus(item.expireDay) === ExpiryStatus.EXPIRING_SOON);
    
    // 检测新增的过期物品
    const newExpiredItems: Item[] = [];
    const newExpiringItems: Item[] = [];
    
    // 检查新增的已过期物品
    expiredItems.forEach(item => {
      if (item.id !== undefined && !this.lastNotifiedExpiredIds.has(item.id)) {
        newExpiredItems.push(item);
        this.lastNotifiedExpiredIds.add(item.id);
      }
    });
    
    // 检查新增的即将过期物品
    expiringItems.forEach(item => {
      if (item.id !== undefined && !this.lastNotifiedExpiringSoonIds.has(item.id)) {
        newExpiringItems.push(item);
        this.lastNotifiedExpiringSoonIds.add(item.id);
      }
    });
    
    // 清理已不存在的物品ID（物品已被删除或状态改变）
    this.cleanupNotifiedIds(items);
    
    // 只有当有新增的过期或即将过期物品时才发送通知
    if (newExpiredItems.length > 0 || newExpiringItems.length > 0) {
      // 按用户要求的新格式构建通知内容，显示总的过期物品数量
      const title = '过期提醒';
      const content = `您有${expiringItems.length}件物品即将过期，${expiredItems.length}件物品已过期`;
      
      // 构建详细内容，传入新增物品用于标识
      const detailContent = this.buildExpiryDetailContent(expiredItems, expiringItems, newExpiredItems, newExpiringItems);
      
      // 添加通知
      this.addNotification(title, content, NotificationType.EXPIRY, undefined, detailContent);
    }
  }
  
  /**
   * 构建过期详情内容
   * @param expiredItems 已过期物品列表
   * @param expiringItems 即将过期物品列表
   * @param newExpiredItems 新增已过期物品列表
   * @param newExpiringItems 新增即将过期物品列表
   * @returns 详情内容
   */
  private buildExpiryDetailContent(expiredItems: Item[], expiringItems: Item[], newExpiredItems: Item[], newExpiringItems: Item[]): string {
    let content = '';
    
    // 创建新增物品ID集合，用于标识
    const newExpiredIds = new Set(newExpiredItems.map(item => item.id));
    const newExpiringIds = new Set(newExpiringItems.map(item => item.id));
    
    // 先添加即将过期物品（与通知标题格式保持一致）
    if (expiringItems.length > 0) {
      content += '即将过期物品：\n';
      expiringItems.forEach((item, index) => {
        const isNew = newExpiringIds.has(item.id);
        const newLabel = isNew ? ' [新增]' : '';
        content += `${index + 1}. ${item.name} - ${item.expireDay}天后过期 - 存放位置：${item.location}${newLabel}\n`;
      });
    }
    
    // 后添加已过期物品
    if (expiredItems.length > 0) {
      if (content) {
        content += '\n';
      }
      content += '已过期物品：\n';
      expiredItems.forEach((item, index) => {
        const isNew = newExpiredIds.has(item.id);
        const newLabel = isNew ? ' [新增]' : '';
        content += `${index + 1}. ${item.name} - 存放位置：${item.location}${newLabel}\n`;
      });
    }
    
    return content;
  }
  
  /**
   * 清理已不存在的物品ID（物品已被删除或状态改变）
   * @param items 当前物品列表
   */
  private cleanupNotifiedIds(items: Item[]): void {
    const currentItemIds = new Set(items.map(item => item.id).filter(id => id !== undefined) as number[]);
    const currentExpiredIds = new Set(
      items.filter(item => this.getExpiryStatus(item.expireDay) === ExpiryStatus.EXPIRED && item.id !== undefined)
           .map(item => item.id as number)
    );
    const currentExpiringIds = new Set(
      items.filter(item => this.getExpiryStatus(item.expireDay) === ExpiryStatus.EXPIRING_SOON && item.id !== undefined)
           .map(item => item.id as number)
    );
    
    // 清理已删除的物品ID
    this.lastNotifiedExpiredIds.forEach(id => {
      if (!currentItemIds.has(id)) {
        this.lastNotifiedExpiredIds.delete(id);
      }
    });
    
    this.lastNotifiedExpiringSoonIds.forEach(id => {
      if (!currentItemIds.has(id)) {
        this.lastNotifiedExpiringSoonIds.delete(id);
      }
    });
    
    // 清理状态已改变的物品ID
    this.lastNotifiedExpiredIds.forEach(id => {
      if (!currentExpiredIds.has(id)) {
        this.lastNotifiedExpiredIds.delete(id);
      }
    });
    
    this.lastNotifiedExpiringSoonIds.forEach(id => {
      if (!currentExpiringIds.has(id)) {
        this.lastNotifiedExpiringSoonIds.delete(id);
      }
    });
  }

  /**
   * 获取过期状态
   * @param days 剩余天数
   * @returns 过期状态
   */
  private getExpiryStatus(days: number): ExpiryStatus {
    if (days <= 0) {
      return ExpiryStatus.EXPIRED;
    } else if (days <= 7) {
      return ExpiryStatus.EXPIRING_SOON;
    } else {
      return ExpiryStatus.VALID;
    }
  }
} 