/**
 * Web页面组件
 * 用于显示网页内容，如用户协议、隐私政策等
 */

import router from '@ohos.router';
import webview from '@ohos.web.webview';
import promptAction from '@ohos.promptAction';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';
// 移除 import { TextAlign } from '@ohos.global.resource';

// 定义进度变化事件接口
interface ProgressChangeEvent {
  newProgress: number;
}

@Entry
@Component
struct WebView {
  @State title: string = '';
  @State url: string = '';
  @State isLoading: boolean = true;
  @State progress: number = 0;
  webController: webview.WebviewController = new webview.WebviewController();
  
  // 主题相关状态
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as Record<string, string>;
    if (params) {
      if (params.title) {
        this.title = params.title;
      }
      if (params.url) {
        this.url = params.url;
      }
    }
    
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      // 加载当前主题设置
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      const isDark = await this.themeManager.isDarkMode();
      this.updateTheme(isDark);
    } catch (err) {
      console.error('加载主题设置失败', err instanceof Error ? err.message : String(err));
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('WebView页面主题更新', isDark ? '深色模式' : '浅色模式');
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.menu_icon'))
          .width(24)
          .height(24)
          .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
          .onClick(() => {
            router.back();
          })
        Text(this.title)
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .fontColor(this.colors.PRIMARY_TEXT)
          .margin({ left: 8 })
      }
      .width('100%')
      .height(50)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(this.colors.CARD_BACKGROUND)
      
      // 加载进度条
      if (this.isLoading) {
        Progress({ value: this.progress, total: 100 })
          .color(this.colors.PRIMARY_COLOR)
          .height(3)
          .width('100%')
      }
      
      // 网页内容
      if (this.url) {
        Web({ src: this.url, controller: this.webController })
          .width('100%')
          .layoutWeight(1)
          .backgroundColor(this.colors.PAGE_BACKGROUND)
          .domStorageAccess(true) // 启用DOM存储访问
          .userAgent('Mozilla/5.0 (Linux; Android 10; HarmonyOS) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.105 HuaweiBrowser/12.1.1.304 Mobile Safari/537.36') // 设置用户代理
          .fileAccess(true) // 允许访问文件
          .javaScriptAccess(true) // 启用JavaScript
          .zoomAccess(true) // 允许缩放
          .onPageBegin(() => {
            this.isLoading = true;
            console.info(`WebView开始加载页面: ${this.url}`);
          })
          .onPageEnd(() => {
            this.isLoading = false;
            console.info(`WebView页面加载完成: ${this.url}`);
          })
          .onProgressChange((event: ProgressChangeEvent) => {
            this.progress = event.newProgress;
            console.info(`WebView加载进度: ${event.newProgress}%`);
          })
          .onErrorReceive((error) => {
            console.error(`WebView加载错误: ${JSON.stringify(error)}`);
            this.isLoading = false;
            promptAction.showToast({ message: `页面加载失败: ${JSON.stringify(error) || '未知错误'}` });
          })
      } else {
        Column() {
          Image($r('app.media.plant_box'))
            .width(120)
            .height(120)
            .margin({ bottom: 20 })
          
          Text('无效的链接')
            .fontSize(18)
            .fontColor(this.colors.PRIMARY_TEXT)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .backgroundColor(this.colors.PAGE_BACKGROUND)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
  }
}