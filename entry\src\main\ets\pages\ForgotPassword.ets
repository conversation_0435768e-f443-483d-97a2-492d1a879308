/**
 * 忘记密码页面设计
 */

import { ForgotPasswordViewModel } from '../viewmodel/ForgotPasswordViewModel';
import promptAction from '@ohos.promptAction';
import router from '@ohos.router';

@Entry
@Component
struct ForgotPassword {
  @State forgotPasswordViewModel: ForgotPasswordViewModel = new ForgotPasswordViewModel();
  @State phoneNumber: string = '';
  @State verificationCode: string = '';
  @State newPassword: string = '';
  @State confirmPassword: string = '';
  @State isAgreeProtocol: boolean = false;
  @State isLoading: boolean = false;
  @State showNewPassword: boolean = false;
  @State showConfirmPassword: boolean = false;
  
  // 倒计时相关状态
  @State countdownTime: number = 60;
  @State isCounting: boolean = false;
  @State btnText: string = '发送验证码';
  private intervalID: number = -1;
  
  aboutToAppear() {
    // 初始化视图模型
  }
  
  aboutToDisappear() {
    // 清理定时器
    if (this.intervalID !== -1) {
      clearInterval(this.intervalID);
    }
  }
  
  // 开始倒计时方法
  startCountdown() {
    if (this.isCounting) return;
    
    // 检查手机号是否有效
    if (!this.phoneNumber || this.phoneNumber.length !== 11) {
      promptAction.showToast({ message: '请输入有效的手机号码' });
      return;
    }
    
    // 发送验证码
    this.forgotPasswordViewModel.sendVerificationCode().then(result => {
      if (result.isSuccess) {
        promptAction.showToast({ message: result.message });
        
        // 开始倒计时
        this.isCounting = true;
        this.countdownTime = 60;
        this.btnText = `${this.countdownTime}秒后重试`;
        
        this.intervalID = setInterval(() => {
          this.countdownTime--;
          this.btnText = `${this.countdownTime}秒后重试`;
          
          if (this.countdownTime <= 0) {
            clearInterval(this.intervalID);
            this.isCounting = false;
            this.btnText = '发送验证码';
          }
        }, 1000);
      } else {
        promptAction.showToast({ message: result.message });
      }
    });
  }

  build() {
    Column() {
      // 顶部欢迎文本
      Column() {
        Text('HELLO,')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 20, bottom: 15 })
          .alignSelf(ItemAlign.Start)
        
        Text('重置您的密码')
          .fontSize(25)
          .fontWeight(FontWeight.Bold)
          .margin({ bottom: 20 })
          .alignSelf(ItemAlign.Start)
      }
      .width('70%')
      
      // 中间图片 - 植物盒子
      Image($r('app.media.plant_box'))
        .width('auto')
        .height(180)
        .margin({ bottom: 20 })

      // 重置密码表单
      Column() {
        // 手机号输入框
        Column() {
          TextInput({ placeholder: '请输入手机号' })
            .height(50)
            .width('100%')
            .backgroundColor('#F5F5F5')
            .borderRadius(24)
            .padding({ left: 16, right: 16 })
            .margin({ bottom: 10 })
            .maxLength(11)
            .inputFilter('^[0-9]*$')
            .onChange((value: string) => {
              this.phoneNumber = value;
              this.forgotPasswordViewModel.setUsername(value);
            })
        }
        .width('90%')
        
        // 验证码输入框和发送按钮
        Row() {
          TextInput({ placeholder: '请输入验证码' })
            .height(50)
            .backgroundColor('#F5F5F5')
            .borderRadius(24)
            .padding({ left: 16, right: 16 })
            .maxLength(6)
            .inputFilter('^[0-9]*$')
            .onChange((value: string) => {
              this.verificationCode = value;
              this.forgotPasswordViewModel.setVerificationCode(value);
            })
            .layoutWeight(1)
          
          Button(this.btnText)
            .height(50)
            .fontSize(14)
            .borderRadius(24)
            .backgroundColor(this.isCounting ? '#CCCCCC' : '#A9BE82')
            .fontColor(Color.White)
            .margin({ left: 10 })
            .enabled(!this.isCounting)
            .onClick(() => this.startCountdown())
            .width('35%')
        }
        .width('90%')
        .margin({ bottom: 10 })
        
        // 新密码输入框
        TextInput({ placeholder: '请输入新密码' })
          .type(this.showNewPassword ? InputType.Normal : InputType.Password)
          .height(50)
          .width('90%')
          .backgroundColor('#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .onChange((value: string) => {
            this.newPassword = value;
            this.forgotPasswordViewModel.setNewPassword(value);
          })
        
        // 确认新密码输入框
        TextInput({ placeholder: '请确认新密码' })
          .type(this.showConfirmPassword ? InputType.Normal : InputType.Password)
          .height(50)
          .width('90%')
          .backgroundColor('#F5F5F5')
          .borderRadius(24)
          .padding({ left: 16, right: 16 })
          .margin({ bottom: 10 })
          .onChange((value: string) => {
            this.confirmPassword = value;
            this.forgotPasswordViewModel.setConfirmPassword(value);
          })
        
        // 重置密码按钮
        Button('重置密码')
          .width('90%')
          .height(50)
          .borderRadius(24)
          .backgroundColor('#A9BE82')
          .fontColor(Color.White)
          .fontSize(16)
          .enabled(!this.isLoading && this.isAgreeProtocol)
          .opacity(this.isAgreeProtocol ? 1 : 0.5)
          .onClick(() => this.handleResetPassword())
          .margin({ bottom: 10 })
        
        // 返回登录文本链接
        Row() {
          Text('返回登录')
            .fontSize(14)
            .fontColor('#666666')
            .onClick(() => this.backToLogin())
        }
        .width('90%')
        .justifyContent(FlexAlign.Center)
        .margin({ bottom: 10 })
      }
      .width('90%')
      
      // 底部协议和技术支持
      Column() {
        // 底部协议
        Column() {
          // 第一行
          Row() {
            Checkbox()
              .select(this.isAgreeProtocol)
              .onChange((value: boolean) => {
                this.isAgreeProtocol = value;
                this.forgotPasswordViewModel.setAgreeProtocol(value);
              })
              .margin({ right: 5 })
              
            Text() {
              Span('我已阅读并同意')
                .fontColor('#666666')
              Span('《用户协议》')
                .onClick(() => this.openUserAgreement())
                .fontColor('#0972d3')
                .fontWeight(FontWeight.Medium)
            }
            .fontSize(13)
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          
          // 第二行
          Row() {
            Text() {
              Span('和')
                .fontColor('#666666')
              Span('《隐私政策》')
                .onClick(() => this.openPrivacyPolicy())
                .fontColor('#0972d3')
                .fontWeight(FontWeight.Medium)
            }
            .fontSize(13)
            .margin({ left: 29 }) // 与第一行文字对齐
          }
          .width('100%')
          .justifyContent(FlexAlign.Center)
          .margin({ top: 5 })
        }
        .width('90%')
        .margin({ bottom: 5 })
        .padding({ top: 5, bottom: 5 })
      }
      .width('100%')
      .position({ bottom: 10 })
      .margin({ top: 20 })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Color.White)
  }
  
  /**
   * 处理重置密码逻辑
   */
  handleResetPassword() {
    // 验证码和手机号验证
    if (!this.phoneNumber || this.phoneNumber.length !== 11) {
      promptAction.showToast({ message: '请输入有效的手机号码' });
      return;
    }
    
    if (!this.newPassword || this.newPassword.length < 6) {
      promptAction.showToast({ message: '请输入至少6位的新密码' });
      return;
    }
    
    if (this.newPassword !== this.confirmPassword) {
      promptAction.showToast({ message: '两次输入的密码不一致' });
      return;
    }
    
    if (!this.verificationCode || this.verificationCode.length !== 6) {
      promptAction.showToast({ message: '请输入6位验证码' });
      return;
    }
    
    this.isLoading = true;
    this.forgotPasswordViewModel.setLoadingState(true);
    
    this.forgotPasswordViewModel.resetPassword().then(result => {
      if (result.isSuccess) {
        promptAction.showToast({ message: result.message });
        // 密码重置成功后跳转到登录页面
        router.pushUrl({
          url: 'pages/Login'
        }).catch((err: Error) => {
          console.error(`跳转到登录页面失败: ${err.message}`);
          promptAction.showToast({ message: `跳转到登录页面失败: ${err.message}` });
        });
      } else {
        promptAction.showToast({ message: result.message });
      }
    }).catch((error: Error) => {
      promptAction.showToast({ message: '密码重置失败: ' + error.message });
    }).finally(() => {
      this.isLoading = false;
      this.forgotPasswordViewModel.setLoadingState(false);
    });
  }
  
  /**
   * 返回登录页面
   */
  backToLogin() {
    router.back();
  }

  /**
   * 打开用户协议
   */
  openUserAgreement() {
    // 跳转到WebView页面打开用户协议
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '用户协议',
        url: 'https://example.com/user_agreement.html'
      }
    }).catch((err: Error) => {
      console.error(`打开用户协议失败: ${err.message}`);
      promptAction.showToast({ message: `打开用户协议失败: ${err.message}` });
    });
  }

  /**
   * 打开隐私政策
   */
  openPrivacyPolicy() {
    // 跳转到WebView页面打开隐私政策
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '隐私政策',
        url: 'https://example.com/privacy_policy.html'
      }
    }).catch((err: Error) => {
      console.error(`打开隐私政策失败: ${err.message}`);
      promptAction.showToast({ message: `打开隐私政策失败: ${err.message}` });
    });
  }
}