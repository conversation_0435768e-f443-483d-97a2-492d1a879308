import preferences from '@ohos.data.preferences';
import { ThemeConstants, ThemeColors } from '../constants/ThemeConstants';

/**
 * 主题管理器
 * 负责存储和获取应用主题设置
 */
export class ThemeManager {
  private static readonly PREFERENCES_NAME: string = 'app_preferences';
  private static readonly THEME_KEY: string = 'app_theme';
  private static instance: ThemeManager;
  private preferences: preferences.Preferences | null = null;
  
  /**
   * 获取单例实例
   */
  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }
  
  /**
   * 初始化首选项
   */
  public async init(): Promise<void> {
    try {
      this.preferences = await preferences.getPreferences(getContext(this), ThemeManager.PREFERENCES_NAME);
      console.info('主题管理器初始化成功');
    } catch (err) {
      console.error('主题管理器初始化失败', err);
    }
  }
  
  /**
   * 获取当前主题
   * @returns 主题名称，默认浅色主题
   */
  public async getTheme(): Promise<string> {
    if (!this.preferences) {
      await this.init();
    }
    
    try {
      const theme = await this.preferences?.get(ThemeManager.THEME_KEY, ThemeConstants.THEME_LIGHT) as string;
      return theme;
    } catch (err) {
      console.error('获取主题失败', err);
      return ThemeConstants.THEME_LIGHT;
    }
  }
  
  /**
   * 设置主题
   * @param theme 主题名称
   */
  public async setTheme(theme: string): Promise<void> {
    if (!this.preferences) {
      await this.init();
    }
    
    try {
      await this.preferences?.put(ThemeManager.THEME_KEY, theme);
      await this.preferences?.flush();
      console.info('主题设置成功', theme);
    } catch (err) {
      console.error('主题设置失败', err);
    }
  }
  
  /**
   * 切换主题
   * 从浅色切换到深色，或从深色切换到浅色
   */
  public async toggleTheme(): Promise<string> {
    const currentTheme = await this.getTheme();
    const newTheme = currentTheme === ThemeConstants.THEME_LIGHT ? 
      ThemeConstants.THEME_DARK : ThemeConstants.THEME_LIGHT;
    
    await this.setTheme(newTheme);
    return newTheme;
  }
  
  /**
   * 获取主题颜色
   * @param theme 主题名称
   * @returns 主题颜色配置
   */
  public getThemeColors(theme: string): ThemeColors {
    return theme === ThemeConstants.THEME_DARK ? 
      ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
  }
} 