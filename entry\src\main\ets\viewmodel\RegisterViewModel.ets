import { UserModel, UserService, LoginResult } from '../model/UserModel';

/**
 * 表单验证结果接口
 */
export interface FormValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * 注册页面的ViewModel
 */
export class RegisterViewModel {
  // 用户数据模型
  private userModel: UserModel = new UserModel();
  // 用户服务
  private userService: UserService = new UserService();
  // 确认密码
  private confirmPassword: string = '';
  // 验证码
  private verificationCode: string = '';
  // 注册状态
  private isLoading: boolean = false;

  /**
   * 设置用户名
   * @param username 用户名
   */
  setUsername(username: string): void {
    this.userModel.username = username;
  }

  /**
   * 设置密码
   * @param password 密码
   */
  setPassword(password: string): void {
    this.userModel.password = password;
  }

  /**
   * 设置确认密码
   * @param confirmPassword 确认密码
   */
  setConfirmPassword(confirmPassword: string): void {
    this.confirmPassword = confirmPassword;
  }

  /**
   * 设置验证码
   * @param code 验证码
   */
  setVerificationCode(code: string): void {
    this.verificationCode = code;
  }

  /**
   * 设置是否同意协议
   * @param isAgree 是否同意
   */
  setAgreeProtocol(isAgree: boolean): void {
    this.userModel.isAgreeProtocol = isAgree;
  }

  /**
   * 获取用户名
   */
  getUsername(): string {
    return this.userModel.username;
  }

  /**
   * 获取密码
   */
  getPassword(): string {
    return this.userModel.password;
  }

  /**
   * 获取确认密码
   */
  getConfirmPassword(): string {
    return this.confirmPassword;
  }

  /**
   * 获取验证码
   */
  getVerificationCode(): string {
    return this.verificationCode;
  }

  /**
   * 获取是否同意协议
   */
  getAgreeProtocol(): boolean {
    return this.userModel.isAgreeProtocol;
  }

  /**
   * 获取注册状态
   */
  getLoadingState(): boolean {
    return this.isLoading;
  }

  /**
   * 设置注册状态
   */
  setLoadingState(isLoading: boolean): void {
    this.isLoading = isLoading;
  }

  /**
   * 验证注册表单
   */
  validateForm(): FormValidationResult {
    if (!this.userModel.username || this.userModel.username.trim() === '') {
      return { isValid: false, message: '请输入手机号' };
    }
    if (this.userModel.username.length !== 11) {
      return { isValid: false, message: '请输入有效的手机号码' };
    }
    if (!this.userModel.password || this.userModel.password.trim() === '') {
      return { isValid: false, message: '请输入密码' };
    }
    if (!this.confirmPassword || this.confirmPassword.trim() === '') {
      return { isValid: false, message: '请输入确认密码' };
    }
    if (this.userModel.password !== this.confirmPassword) {
      return { isValid: false, message: '两次输入的密码不一致' };
    }
    if (!this.verificationCode || this.verificationCode.trim() === '') {
      return { isValid: false, message: '请输入验证码' };
    }
    if (this.verificationCode.length !== 6) {
      return { isValid: false, message: '请输入6位验证码' };
    }
    if (!this.userModel.isAgreeProtocol) {
      return { isValid: false, message: '请阅读并同意用户协议' };
    }
    return { isValid: true, message: '' };
  }

  /**
   * 发送验证码
   * @returns 发送结果Promise
   */
  sendVerificationCode(): Promise<LoginResult> {
    if (!this.userModel.username || this.userModel.username.length !== 11) {
      return Promise.resolve(new LoginResult(false, '请输入有效的手机号码'));
    }
    
    // 模拟发送验证码
    return Promise.resolve(new LoginResult(true, '验证码已发送'));
  }

  /**
   * 注册方法
   * @returns 注册结果Promise
   */
  register(): Promise<LoginResult> {
    const validation: FormValidationResult = this.validateForm();
    if (!validation.isValid) {
      return Promise.resolve(new LoginResult(false, validation.message));
    }

    this.isLoading = true;
    return this.userService.register(this.userModel.username, this.userModel.password)
      .finally(() => {
        this.isLoading = false;
      });
  }
}