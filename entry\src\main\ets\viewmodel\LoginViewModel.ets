import { UserModel, UserService, LoginResult } from '../model/UserModel';

/**
 * 表单验证结果接口
 */
export interface FormValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * 登录页面的ViewModel
 */
export class LoginViewModel {
  // 用户数据模型
  private userModel: UserModel = new UserModel();
  // 用户服务
  private userService: UserService = new UserService();
  // 登录状态
  private isLoading: boolean = false;

  /**
   * 设置用户名
   * @param username 用户名
   */
  setUsername(username: string): void {
    this.userModel.username = username;
  }

  /**
   * 设置密码
   * @param password 密码
   */
  setPassword(password: string): void {
    this.userModel.password = password;
  }

  /**
   * 设置是否同意协议
   * @param isAgree 是否同意
   */
  setAgreeProtocol(isAgree: boolean): void {
    this.userModel.isAgreeProtocol = isAgree;
  }

  /**
   * 获取用户名
   */
  getUsername(): string {
    return this.userModel.username;
  }

  /**
   * 获取密码
   */
  getPassword(): string {
    return this.userModel.password;
  }

  /**
   * 获取是否同意协议
   */
  getAgreeProtocol(): boolean {
    return this.userModel.isAgreeProtocol;
  }

  /**
   * 获取登录状态
   */
  getLoadingState(): boolean {
    return this.isLoading;
  }

  /**
   * 设置登录状态
   */
  setLoadingState(isLoading: boolean): void {
    this.isLoading = isLoading;
  }

  /**
   * 验证登录表单
   */
  validateForm(): FormValidationResult {
    if (!this.userModel.username || this.userModel.username.trim() === '') {
      return { isValid: false, message: '请输入用户名' };
    }
    if (!this.userModel.password || this.userModel.password.trim() === '') {
      return { isValid: false, message: '请输入密码' };
    }
    if (!this.userModel.isAgreeProtocol) {
      return { isValid: false, message: '请阅读并同意用户协议' };
    }
    return { isValid: true, message: '' };
  }

  /**
   * 登录方法
   * @returns 登录结果Promise
   */
  login(): Promise<LoginResult> {
    const validation: FormValidationResult = this.validateForm();
    if (!validation.isValid) {
      return Promise.resolve(new LoginResult(false, validation.message));
    }

    this.isLoading = true;
    return this.userService.login(this.userModel.username, this.userModel.password)
      .finally(() => {
        this.isLoading = false;
      });
  }

  /**
   * 华为账号一键登录方法
   * @returns 登录结果Promise
   */
  huaweiLogin(): Promise<LoginResult> {
    // 验证是否同意协议
    if (!this.userModel.isAgreeProtocol) {
      return Promise.resolve(new LoginResult(false, '请阅读并同意用户协议'));
    }

    this.isLoading = true;
    // 这里应该调用华为账号服务的实际API
    // 目前使用模拟实现
    return new Promise<LoginResult>((resolve) => {
      setTimeout(() => {
        resolve(new LoginResult(true, '华为账号登录成功'));
      }, 1500);
    }).finally(() => {
      this.isLoading = false;
    });
  }

  /**
   * 注册方法
   * @returns 注册结果Promise
   */
  register(): Promise<LoginResult> {
    // 返回成功结果，实际跳转逻辑在页面中处理
    return Promise.resolve(new LoginResult(true, '正在跳转到注册页面'));
  }

  /**
   * 忘记密码方法
   * @returns 操作结果Promise
   */
  forgetPassword(): Promise<LoginResult> {
    // 模拟忘记密码流程
    return Promise.resolve(new LoginResult(true, '已跳转到密码重置页面'));
  }
}