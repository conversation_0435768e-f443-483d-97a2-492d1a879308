import preferences from '@ohos.data.preferences';

/**
 * 用户管理器
 * 负责存储和获取用户配置信息
 */
export class UserManager {
  private static readonly PREFERENCES_NAME: string = 'user_preferences';
  private static readonly USERNAME_KEY: string = 'user_name';
  private static instance: UserManager;
  private preferences: preferences.Preferences | null = null;
  private usernameChangeCallbacks: ((username: string) => void)[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(): UserManager {
    if (!UserManager.instance) {
      UserManager.instance = new UserManager();
    }
    return UserManager.instance;
  }

  /**
   * 初始化首选项
   */
  public async init(): Promise<void> {
    try {
      this.preferences = await preferences.getPreferences(getContext(this), UserManager.PREFERENCES_NAME);
      console.info('用户管理器初始化成功');
    } catch (err) {
      console.error('用户管理器初始化失败', err);
    }
  }

  /**
   * 获取用户名
   * @returns 用户名，默认为 'XXX'
   */
  public async getUsername(): Promise<string> {
    if (!this.preferences) {
      await this.init();
    }

    try {
      const username = await this.preferences?.get(UserManager.USERNAME_KEY, 'XXX') as string;
      return username;
    } catch (err) {
      console.error('获取用户名失败', err);
      return 'XXX';
    }
  }

  /**
   * 设置用户名
   * @param username 用户名
   */
  public async setUsername(username: string): Promise<void> {
    if (!this.preferences) {
      await this.init();
    }

    try {
      await this.preferences?.put(UserManager.USERNAME_KEY, username);
      await this.preferences?.flush();
      console.info('用户名设置成功', username);

      // 通知用户名变化
      this.notifyUsernameChange(username);
    } catch (err) {
      console.error('用户名设置失败', err);
    }
  }

  /**
   * 注册用户名变化回调
   * @param callback 回调函数
   */
  public registerUsernameChangeCallback(callback: (username: string) => void): void {
    this.usernameChangeCallbacks.push(callback);
  }

  /**
   * 移除用户名变化回调
   * @param callback 回调函数
   */
  public unregisterUsernameChangeCallback(callback: (username: string) => void): void {
    const index = this.usernameChangeCallbacks.indexOf(callback);
    if (index !== -1) {
      this.usernameChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 通知用户名变化
   * @param username 新的用户名
   */
  private notifyUsernameChange(username: string): void {
    this.usernameChangeCallbacks.forEach(callback => {
      callback(username);
    });
  }
} 