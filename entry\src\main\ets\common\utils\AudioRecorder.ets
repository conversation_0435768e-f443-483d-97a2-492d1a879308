import audio from '@ohos.multimedia.audio';
import fs from '@ohos.file.fs';
import common from '@ohos.app.ability.common';
import { speechRecognizer } from '@kit.CoreSpeechKit';
import { BusinessError } from '@kit.BasicServicesKit';

/**
 * 音频录制工具类
 * 提供音频录制和语音识别功能（使用HarmonyOS AudioCapturer）
 */
export class AudioRecorder {
  private static instance: AudioRecorder;
  private isRecording: boolean = false;
  private recordingStartTime: number = 0;
  private audioCapturer: audio.AudioCapturer | null = null;
  private audioStreamInfo: audio.AudioStreamInfo;
  private audioCapturerOptions: audio.AudioCapturerOptions;
  private audioFile: fs.File | null = null;
  private audioFilePath: string = '';

  private constructor() {
    // 配置音频流信息
    this.audioStreamInfo = {
      samplingRate: audio.AudioSamplingRate.SAMPLE_RATE_16000, // 16kHz采样率
      channels: audio.AudioChannel.CHANNEL_1, // 单声道
      sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE, // 16位PCM
      encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW // 原始PCM数据
    };

    // 配置音频采集器选项
    this.audioCapturerOptions = {
      streamInfo: this.audioStreamInfo,
      capturerInfo: {
        source: audio.SourceType.SOURCE_TYPE_MIC, // 麦克风输入
        capturerFlags: 0 // 默认标志
      }
    };
  }

  public static getInstance(): AudioRecorder {
    if (!AudioRecorder.instance) {
      AudioRecorder.instance = new AudioRecorder();
    }
    return AudioRecorder.instance;
  }

  /**
   * 初始化音频录制器（使用HarmonyOS AudioCapturer）
   */
  public async initRecorder(): Promise<void> {
    try {
      // 创建AudioCapturer实例
      this.audioCapturer = await audio.createAudioCapturer(this.audioCapturerOptions);

      if (this.audioCapturer) {
        // 设置状态变化监听器
        this.audioCapturer.on('stateChange', (state: audio.AudioState) => {
          console.info(`AudioCapturer状态变化: ${state}`);
        });

        // 设置数据读取监听器
        this.audioCapturer.on('readData', (buffer: ArrayBuffer) => {
          console.info(`AudioCapturer数据读取: ${buffer.byteLength} bytes`);
        });
      }

      console.info('音频录制器初始化成功（HarmonyOS AudioCapturer）');
    } catch (error) {
      console.error('音频录制器初始化失败:', error);
      throw new Error('音频录制器初始化失败');
    }
  }

  /**
   * 开始录音（使用HarmonyOS AudioCapturer）
   */
  public async startRecording(): Promise<void> {
    if (this.isRecording) {
      console.warn('已经在录音中');
      return;
    }

    if (!this.audioCapturer) {
      throw new Error('音频录制器未初始化');
    }

    try {
      // 创建音频文件
      const context = getContext();
      const cacheDir = context.cacheDir;
      this.audioFilePath = `${cacheDir}/audio_record_${Date.now()}.pcm`;

      this.audioFile = await fs.open(this.audioFilePath, fs.OpenMode.CREATE | fs.OpenMode.WRITE_ONLY);

      // 开始录音
      await this.audioCapturer.start();
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // 开始读取音频数据
      this.readAudioData();

      console.info('开始录音（HarmonyOS AudioCapturer）');
    } catch (error) {
      console.error('开始录音失败:', error);
      throw new Error('开始录音失败');
    }
  }

  /**
   * 读取音频数据并写入文件
   */
  private async readAudioData(): Promise<void> {
    if (!this.audioCapturer || !this.audioFile || !this.isRecording) {
      return;
    }

    try {
      const bufferSize = await this.audioCapturer.getBufferSize();

      while (this.isRecording) {
        try {
          const buffer = await this.audioCapturer.read(bufferSize, true);
          if (buffer && buffer.byteLength > 0) {
            // 将音频数据写入文件
            await fs.write(this.audioFile.fd, buffer);
          }
        } catch (readError) {
          console.error('读取音频数据失败:', readError);
          break;
        }
      }
    } catch (error) {
      console.error('读取音频数据失败:', error);
    }
  }

  /**
   * 停止录音（使用HarmonyOS AudioCapturer）
   */
  public async stopRecording(): Promise<string> {
    if (!this.isRecording) {
      throw new Error('没有正在进行的录音');
    }

    if (!this.audioCapturer) {
      throw new Error('音频录制器未初始化');
    }

    try {
      this.isRecording = false;

      // 停止录音
      await this.audioCapturer.stop();

      // 关闭音频文件
      if (this.audioFile) {
        await fs.close(this.audioFile);
        this.audioFile = null;
      }

      const recordingDuration = Date.now() - this.recordingStartTime;
      console.info('录音完成，文件路径:', this.audioFilePath, '录音时长:', recordingDuration, 'ms');

      return this.audioFilePath;
    } catch (error) {
      console.error('停止录音失败:', error);
      throw new Error('停止录音失败');
    }
  }

  /**
   * 释放录音器资源（使用HarmonyOS AudioCapturer）
   */
  public async release(): Promise<void> {
    try {
      this.isRecording = false;

      // 关闭音频文件
      if (this.audioFile) {
        await fs.close(this.audioFile);
        this.audioFile = null;
      }

      // 释放AudioCapturer资源
      if (this.audioCapturer) {
        await this.audioCapturer.release();
        this.audioCapturer = null;
      }

      console.info('音频录制器资源已释放（HarmonyOS AudioCapturer）');
    } catch (error) {
      console.error('释放音频录制器资源失败:', error);
      throw new Error('释放音频录制器资源失败');
    }
  }

  /**
   * 检查是否正在录音
   */
  public isRecordingNow(): boolean {
    return this.isRecording;
  }
}

/**
 * 语音识别服务类
 * 支持HarmonyOS语音识别和云端API集成
 */
export class SpeechRecognitionService {
  private static instance: SpeechRecognitionService;
  private apiKey: string = 'YOUR_BAIDU_API_KEY'; // 需要替换为实际的API密钥
  private secretKey: string = 'YOUR_BAIDU_SECRET_KEY'; // 需要替换为实际的密钥
  private accessToken: string = '';
  private useRealAPI: boolean = false; // 是否使用真实API
  private recognitionCallback: ((text: string) => void) | null = null; // 实时识别回调

  private constructor() {}

  public static getInstance(): SpeechRecognitionService {
    if (!SpeechRecognitionService.instance) {
      SpeechRecognitionService.instance = new SpeechRecognitionService();
    }
    return SpeechRecognitionService.instance;
  }

  /**
   * 设置实时识别回调
   */
  public setRecognitionCallback(callback: (text: string) => void): void {
    this.recognitionCallback = callback;
  }

  /**
   * 清除实时识别回调
   */
  public clearRecognitionCallback(): void {
    this.recognitionCallback = null;
  }

  /**
   * 语音识别（支持实时识别和批量识别）
   * @param audioFilePath 音频文件路径
   * @returns 识别结果文本
   */
  public async recognizeSpeech(audioFilePath: string): Promise<string> {
    try {
      console.info('开始语音识别，音频文件路径:', audioFilePath);

      if (this.useRealAPI && this.apiKey !== 'YOUR_BAIDU_API_KEY') {
        // 使用真实API进行识别
        return await this.callRealAPI(audioFilePath);
      } else {
        // 使用模拟识别，支持实时回调
        return await this.mockRecognition(audioFilePath);
      }
    } catch (error) {
      console.error('语音识别失败:', error);
      throw new Error('语音识别失败');
    }
  }

  /**
   * 模拟语音识别（支持实时回调）
   */
  private async mockRecognition(audioFilePath: string): Promise<string> {
    const mockResults = [
      '添加一个苹果到客厅茶几',
      '在厨房冰箱里放一瓶牛奶',
      '把充电宝放在书房抽屉里',
      '客厅电视柜下面放感冒药',
      '卧室床头柜放一本书',
      '把钥匙放在玄关抽屉里',
      '在卧室衣柜里放一件外套',
      '客厅沙发下面有遥控器',
      '厨房橱柜里放调料盒',
      '书房书架上放一本小说'
    ];

    // 模拟实时识别过程
    if (this.recognitionCallback) {
      // 模拟逐步识别过程
      const finalResult = mockResults[Math.floor(Math.random() * mockResults.length)];
      const words = finalResult.split('');
      let currentText = '';

      for (let i = 0; i < words.length; i++) {
        currentText += words[i];
        this.recognitionCallback(currentText);
        await new Promise<void>(resolve => setTimeout(resolve, 100)); // 模拟实时识别延迟
      }

      return finalResult;
    } else {
      // 模拟API调用延迟
      await new Promise<void>((resolve) => {
        setTimeout(() => {
          resolve();
        }, 1000);
      });

      const randomResult = mockResults[Math.floor(Math.random() * mockResults.length)];
      console.info('语音识别完成，结果:', randomResult);

      return randomResult;
    }
  }

  /**
   * 设置API密钥（用于真实API集成）
   */
  public setApiKeys(apiKey: string, secretKey: string): void {
    this.apiKey = apiKey;
    this.secretKey = secretKey;
    this.accessToken = ''; // 清除旧的访问令牌
    this.useRealAPI = true; // 启用真实API
    console.info('API密钥已设置，可以集成真实语音识别服务');
  }

  /**
   * 启用或禁用真实API
   */
  public setUseRealAPI(useReal: boolean): void {
    this.useRealAPI = useReal;
    console.info(`语音识别API模式: ${useReal ? '真实API' : '模拟模式'}`);
  }

  /**
   * 获取百度语音识别访问令牌（真实API实现）
   */
  private async getAccessToken(): Promise<string> {
    if (this.accessToken) {
      return this.accessToken;
    }

    try {
      const url = `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${this.apiKey}&client_secret=${this.secretKey}`;
      
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(url, {
        method: http.RequestMethod.POST,
        header: {
          'Content-Type': 'application/json'
        }
      });

      if (response.responseCode === 200) {
        const result = JSON.parse(response.result.toString()) as AccessTokenResponse;
        this.accessToken = result.access_token;
        return this.accessToken;
      } else {
        throw new Error(`获取访问令牌失败: ${response.responseCode}`);
      }
    } catch (error) {
      console.error('获取访问令牌失败:', error);
      throw new Error('获取访问令牌失败');
    }
  }

  /**
   * 真实语音识别API调用（需要配置API密钥后使用）
   */
  private async callRealAPI(audioFilePath: string): Promise<string> {
    try {
      const accessToken = await this.getAccessToken();
      
      // 调用百度语音识别API
      const url = `https://vop.baidu.com/server_api?cuid=YOUR_DEVICE_ID&token=${accessToken}`;
      
      const httpRequest = http.createHttp();
      const response = await httpRequest.request(url, {
        method: http.RequestMethod.POST,
        header: {
          'Content-Type': 'application/json'
        },
        extraData: JSON.stringify({
          format: 'pcm',
          rate: 16000,
          channel: 1,
          token: accessToken,
          speech: 'base64_audio_data_placeholder',
          len: 1024
        })
      });

      if (response.responseCode === 200) {
        const result = JSON.parse(response.result.toString()) as SpeechRecognitionResponse;
        if (result.err_no === 0 && result.result && result.result.length > 0) {
          return result.result[0];
        } else {
          throw new Error(`语音识别失败: ${result.err_msg || '未知错误'}`);
        }
      } else {
        throw new Error(`API请求失败: ${response.responseCode}`);
      }
    } catch (error) {
      console.error('真实API调用失败:', error);
      throw new Error('真实API调用失败');
    }
  }

  /**
   * 将ArrayBuffer转换为Base64字符串
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    // 使用简单的Base64编码实现
    return this.simpleBase64Encode(binary);
  }

  /**
   * 简单的Base64编码实现
   */
  private simpleBase64Encode(str: string): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    let i = 0;
    
    while (i < str.length) {
      const char1 = str.charCodeAt(i++);
      const char2 = i < str.length ? str.charCodeAt(i++) : 0;
      const char3 = i < str.length ? str.charCodeAt(i++) : 0;
      
      const enc1 = char1 >> 2;
      const enc2 = ((char1 & 3) << 4) | (char2 >> 4);
      const enc3 = ((char2 & 15) << 2) | (char3 >> 6);
      const enc4 = char3 & 63;
      
      result += chars.charAt(enc1) + chars.charAt(enc2) + 
                (char2 ? chars.charAt(enc3) : '=') + 
                (char3 ? chars.charAt(enc4) : '=');
    }
    
    return result;
  }
}
