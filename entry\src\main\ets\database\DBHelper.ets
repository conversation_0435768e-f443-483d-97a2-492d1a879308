import relationalStore from '@ohos.data.relationalStore';
import { DBConstants } from './DBConstants';
import { MedicineItem } from '../model/MedicineItem';
import Logger from '../common/Logger';
import { ItemType } from '../model/ItemType';

const TAG = 'DBHelper';

/**
 * 数据库帮助类，提供数据库操作方法
 */
export class DBHelper {
  private rdbStore: relationalStore.RdbStore | null = null;
  private dbVersion: number = 0;

  constructor() {
  }

  getRdbStore(callback?: Function): void {
    if (this.rdbStore != null && this.dbVersion === DBConstants.DB_VERSION) {
      callback && callback(this.rdbStore);
      return;
    }

    let context = getContext(this);
    const config: relationalStore.StoreConfig = {
      name: DBConstants.DB_NAME,
      securityLevel: relationalStore.SecurityLevel.S1
    };

    Logger.info(TAG, `Getting RdbStore, database version in constants: ${DBConstants.DB_VERSION}, current: ${this.dbVersion}`);

    relationalStore.getRdbStore(context, config, (err: Error, store: relationalStore.RdbStore) => {
      if (err) {
        Logger.error(TAG, `Failed to get RdbStore, err: ${JSON.stringify(err)}`);
        return;
      }
      this.rdbStore = store;
      
      // 检查数据库版本是否需要更新
      if (this.dbVersion !== DBConstants.DB_VERSION) {
        Logger.info(TAG, `Database version updated from ${this.dbVersion} to ${DBConstants.DB_VERSION}, performing migration.`);
        this.createTable(() => {
          // 执行数据库迁移而不是删除重建
          this.migrateDatabase(this.dbVersion, DBConstants.DB_VERSION, () => {
            this.dbVersion = DBConstants.DB_VERSION;
            Logger.info(TAG, `Database migrated successfully to version ${this.dbVersion}`);
            callback && callback(this.rdbStore);
          });
        });
      } else {
        this.createTable(() => {
          Logger.info(TAG, `Table created, database ready`);
          callback && callback(this.rdbStore);
        });
      }
    });
  }

  private dropTable(callback?: Function): void {
    if (this.rdbStore === null) {
      Logger.error(TAG, 'Failed to drop table due to rdbStore is null.');
      return;
    }
    
    const dropSQL = `DROP TABLE IF EXISTS ${DBConstants.ITEM_TABLE}`;
    this.rdbStore.executeSql(dropSQL, null, (err: Error) => {
      if (err) {
        Logger.error(TAG, `Failed to drop table, err: ${JSON.stringify(err)}`);
        return;
      }
      Logger.info(TAG, 'Drop table success.');
      callback && callback();
    });
  }

  private createTable(callback?: Function): void {
    if (this.rdbStore === null) {
      Logger.error(TAG, 'Failed to create table due to rdbStore is null.');
      return;
    }
    this.rdbStore.executeSql(DBConstants.CREATE_ITEM_TABLE, null, (err: Error) => {
      if (err) {
        Logger.error(TAG, `Failed to create table, err: ${JSON.stringify(err)}`);
        return;
      }
      Logger.info(TAG, 'Create table success.');
      callback && callback();
    });
  }

  insertItem(medicineItem: MedicineItem, callback?: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.insertItem(medicineItem, callback);
      });
      return;
    }
    const valueBucket = medicineItem.toValuesBucket();
    Logger.info(TAG, `Inserting item with expiry_unit: ${medicineItem.expiryUnit}, expiry_days: ${medicineItem.expireDay}`);
    this.rdbStore.insert(DBConstants.ITEM_TABLE, valueBucket, (err: Error, rowId: number) => {
      if (err) {
        Logger.error(TAG, `Failed to insert data, err: ${JSON.stringify(err)}`);
        callback && callback(-1);
        return;
      }
      Logger.info(TAG, `Insert data success, rowId: ${rowId}`);
      callback && callback(rowId);
    });
  }

  batchInsertItems(medicineItems: Array<MedicineItem>, callback?: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.batchInsertItems(medicineItems, callback);
      });
      return;
    }
    const valueBuckets = medicineItems.map((item) => item.toValuesBucket());
    this.rdbStore.batchInsert(DBConstants.ITEM_TABLE, valueBuckets, (err: Error, rowId: number) => {
      if (err) {
        Logger.error(TAG, `Failed to batch insert data, err: ${JSON.stringify(err)}`);
        callback && callback(-1);
        return;
      }
      Logger.info(TAG, `Batch insert data success, rowId: ${rowId}`);
      callback && callback(rowId);
    });
  }

  updateItem(id: number, medicineItem: MedicineItem, callback?: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.updateItem(id, medicineItem, callback);
      });
      return;
    }
    const valueBucket = medicineItem.toValuesBucket();
    Logger.info(TAG, `Updating item ID ${id} with expiry_unit: ${medicineItem.expiryUnit}, expiry_days: ${medicineItem.expireDay}`);
    const predicates = new relationalStore.RdbPredicates(DBConstants.ITEM_TABLE);
    predicates.equalTo(DBConstants.COLUMN_ID, id);
    this.rdbStore.update(valueBucket, predicates, (err: Error, rows: number) => {
      if (err) {
        Logger.error(TAG, `Failed to update data, err: ${JSON.stringify(err)}`);
        callback && callback(0);
        return;
      }
      Logger.info(TAG, `Update data success, rows: ${rows}`);
      callback && callback(rows);
    });
  }

  deleteItem(id: number, callback?: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.deleteItem(id, callback);
      });
      return;
    }
    const predicates = new relationalStore.RdbPredicates(DBConstants.ITEM_TABLE);
    predicates.equalTo(DBConstants.COLUMN_ID, id);
    this.rdbStore.delete(predicates, (err: Error, rows: number) => {
      if (err) {
        Logger.error(TAG, `Failed to delete data, err: ${JSON.stringify(err)}`);
        callback && callback(0);
        return;
      }
      Logger.info(TAG, `Delete data success, rows: ${rows}`);
      callback && callback(rows);
    });
  }

  queryAllItems(callback: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.queryAllItems(callback);
      });
      return;
    }
    const predicates = new relationalStore.RdbPredicates(DBConstants.ITEM_TABLE);
    predicates.orderByDesc(DBConstants.COLUMN_UPDATE_TIME);
    this.rdbStore.query(predicates, null, (err: Error, resultSet: relationalStore.ResultSet) => {
      if (err) {
        Logger.error(TAG, `Failed to query all items, err: ${JSON.stringify(err)}`);
        callback([]);
        return;
      }
      
      let medicineItems: Array<MedicineItem> = [];
      try {
        while (resultSet.goToNextRow()) {
          const medicineItem = MedicineItem.fromResultSet(resultSet);
          if (medicineItem) {
            Logger.info(TAG, `Read item: ${medicineItem.name}, expiry_unit: ${medicineItem.expiryUnit}, expiry_days: ${medicineItem.expireDay}`);
            medicineItems.push(medicineItem);
          }
        }
      } catch (e) {
        Logger.error(TAG, `Failed to process result set: ${JSON.stringify(e)}`);
      } finally {
        resultSet.close();
      }
      
      Logger.info(TAG, `Query all items success, count: ${medicineItems.length}`);
      callback(medicineItems);
    });
  }

  queryItemsByType(itemType: ItemType, callback: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.queryItemsByType(itemType, callback);
      });
      return;
    }
    const predicates = new relationalStore.RdbPredicates(DBConstants.ITEM_TABLE);
    predicates.equalTo(DBConstants.COLUMN_ITEM_TYPE, itemType);
    predicates.orderByDesc(DBConstants.COLUMN_UPDATE_TIME);
    this.rdbStore.query(predicates, null, (err: Error, resultSet: relationalStore.ResultSet) => {
      if (err) {
        Logger.error(TAG, `Failed to query items by type, err: ${JSON.stringify(err)}`);
        callback([]);
        return;
      }
      
      let medicineItems: Array<MedicineItem> = [];
      try {
        while (resultSet.goToNextRow()) {
          const medicineItem = MedicineItem.fromResultSet(resultSet);
          if (medicineItem) {
            medicineItems.push(medicineItem);
          }
        }
      } catch (e) {
        Logger.error(TAG, `Failed to process result set: ${JSON.stringify(e)}`);
      } finally {
        resultSet.close();
      }
      
      Logger.info(TAG, `Query items by type success, count: ${medicineItems.length}`);
      callback(medicineItems);
    });
  }

  searchItemsByName(keyword: string, callback: Function): void {
    if (this.rdbStore === null) {
      this.getRdbStore(() => {
        this.searchItemsByName(keyword, callback);
      });
      return;
    }
    const predicates = new relationalStore.RdbPredicates(DBConstants.ITEM_TABLE);
    predicates.like(DBConstants.COLUMN_NAME, `%${keyword}%`);
    predicates.orderByDesc(DBConstants.COLUMN_UPDATE_TIME);
    this.rdbStore.query(predicates, null, (err: Error, resultSet: relationalStore.ResultSet) => {
      if (err) {
        Logger.error(TAG, `Failed to search items by name, err: ${JSON.stringify(err)}`);
        callback([]);
        return;
      }
      
      let medicineItems: Array<MedicineItem> = [];
      try {
        while (resultSet.goToNextRow()) {
          const medicineItem = MedicineItem.fromResultSet(resultSet);
          if (medicineItem) {
            medicineItems.push(medicineItem);
          }
        }
      } catch (e) {
        Logger.error(TAG, `Failed to process result set: ${JSON.stringify(e)}`);
      } finally {
        resultSet.close();
      }
      
      Logger.info(TAG, `Search items by name success, count: ${medicineItems.length}`);
      callback(medicineItems);
    });
  }

  /**
   * 执行数据库迁移
   * @param fromVersion 源版本
   * @param toVersion 目标版本
   * @param callback 回调函数
   */
  private migrateDatabase(fromVersion: number, toVersion: number, callback?: Function): void {
    if (this.rdbStore === null) {
      Logger.error(TAG, 'Failed to migrate database due to rdbStore is null.');
      return;
    }

    Logger.info(TAG, `Migrating database from version ${fromVersion} to ${toVersion}`);
    
    // 从版本3升级到版本4：添加生产日期字段
    if (fromVersion < 4 && toVersion >= 4) {
      this.rdbStore.executeSql(DBConstants.ADD_PRODUCTION_DATE_COLUMN, null, (err: Error) => {
        if (err) {
          Logger.error(TAG, `Failed to add production_date column, err: ${JSON.stringify(err)}`);
          // 如果添加字段失败，可能是字段已经存在，继续执行
          Logger.info(TAG, 'Production date column might already exist, continuing migration.');
        } else {
          Logger.info(TAG, 'Successfully added production_date column.');
        }
        callback && callback();
      });
    } else {
      callback && callback();
    }
  }
} 