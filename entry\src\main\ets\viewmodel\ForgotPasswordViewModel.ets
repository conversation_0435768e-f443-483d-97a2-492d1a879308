import { UserModel, UserService, LoginResult } from '../model/UserModel';

/**
 * 表单验证结果接口
 */
export interface FormValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * 忘记密码页面的ViewModel
 */
export class ForgotPasswordViewModel {
  // 用户数据模型
  private userModel: UserModel = new UserModel();
  // 用户服务
  private userService: UserService = new UserService();
  // 重置密码状态
  private isLoading: boolean = false;
  // 验证码
  private verificationCode: string = '';
  // 新密码
  private newPassword: string = '';
  // 确认新密码
  private confirmPassword: string = '';

  /**
   * 设置用户名
   * @param username 用户名/手机号
   */
  setUsername(username: string): void {
    this.userModel.username = username;
  }

  /**
   * 设置验证码
   * @param code 验证码
   */
  setVerificationCode(code: string): void {
    this.verificationCode = code;
  }

  /**
   * 设置新密码
   * @param password 新密码
   */
  setNewPassword(password: string): void {
    this.newPassword = password;
  }

  /**
   * 设置确认密码
   * @param password 确认密码
   */
  setConfirmPassword(password: string): void {
    this.confirmPassword = password;
  }

  /**
   * 设置是否同意协议
   * @param isAgree 是否同意
   */
  setAgreeProtocol(isAgree: boolean): void {
    this.userModel.isAgreeProtocol = isAgree;
  }

  /**
   * 获取用户名
   */
  getUsername(): string {
    return this.userModel.username;
  }

  /**
   * 获取验证码
   */
  getVerificationCode(): string {
    return this.verificationCode;
  }

  /**
   * 获取是否同意协议
   */
  getAgreeProtocol(): boolean {
    return this.userModel.isAgreeProtocol;
  }

  /**
   * 获取加载状态
   */
  getLoadingState(): boolean {
    return this.isLoading;
  }

  /**
   * 设置加载状态
   */
  setLoadingState(isLoading: boolean): void {
    this.isLoading = isLoading;
  }

  /**
   * 验证重置密码表单
   */
  validateForm(): FormValidationResult {
    if (!this.userModel.username || this.userModel.username.trim() === '') {
      return { isValid: false, message: '请输入手机号' };
    }
    
    if (!this.verificationCode || this.verificationCode.length !== 6) {
      return { isValid: false, message: '请输入6位验证码' };
    }
    
    if (!this.newPassword || this.newPassword.length < 6) {
      return { isValid: false, message: '请输入至少6位的新密码' };
    }
    
    if (this.newPassword !== this.confirmPassword) {
      return { isValid: false, message: '两次输入的密码不一致' };
    }
    
    if (!this.userModel.isAgreeProtocol) {
      return { isValid: false, message: '请阅读并同意用户协议' };
    }
    
    return { isValid: true, message: '' };
  }

  /**
   * 发送验证码
   * @returns 操作结果Promise
   */
  sendVerificationCode(): Promise<LoginResult> {
    if (!this.userModel.username || this.userModel.username.length !== 11) {
      return Promise.resolve(new LoginResult(false, '请输入有效的手机号码'));
    }

    // 模拟发送验证码流程
    return new Promise<LoginResult>((resolve) => {
      setTimeout(() => {
        resolve(new LoginResult(true, '验证码已发送'));
      }, 1000);
    });
  }

  /**
   * 重置密码
   * @returns 操作结果Promise
   */
  resetPassword(): Promise<LoginResult> {
    const validation: FormValidationResult = this.validateForm();
    if (!validation.isValid) {
      return Promise.resolve(new LoginResult(false, validation.message));
    }

    this.isLoading = true;
    
    // 模拟重置密码流程
    return new Promise<LoginResult>((resolve) => {
      setTimeout(() => {
        resolve(new LoginResult(true, '密码重置成功'));
      }, 1500);
    }).finally(() => {
      this.isLoading = false;
    });
  }
} 