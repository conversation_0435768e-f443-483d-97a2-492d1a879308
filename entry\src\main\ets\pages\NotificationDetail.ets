import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';
import { NotificationManager, NotificationItem, NotificationType } from '../common/utils/NotificationManager';
import { DBManager } from '../database/DBManager';

// 路由参数接口
interface RouterParams {
  notificationId: number;
}

/**
 * 通知详情页面
 */
@Entry
@Component
struct NotificationDetail {
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  @State notification: NotificationItem | null = null;
  
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private notificationManager: NotificationManager = NotificationManager.getInstance();
  private dbManager: DBManager = DBManager.getInstance();
  private notificationId: number = 0;
  
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as RouterParams;
    if (params && params.notificationId) {
      this.notificationId = params.notificationId;
      
      // 获取通知详情
      const notifications = this.notificationManager.getNotifications();
      const notification = notifications.find(item => item.id === this.notificationId);
      
      if (notification) {
        this.notification = notification;
        
        // 标记通知为已读
        if (!notification.isRead) {
          this.notificationManager.markAsRead(this.notificationId);
        }
      }
    }
    
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      this.isDarkMode = await this.themeManager.isDarkMode();
      
      // 更新主题颜色
      this.colors = this.isDarkMode ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
      
      console.info('加载主题设置', '是否暗黑模式:', this.isDarkMode);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('NotificationDetail页面主题更新', isDark ? '深色模式' : '浅色模式');
  }
  
  /**
   * 返回上一页
   */
  private goBack() {
    router.back();
  }
  
  /**
   * 查看相关物品详情
   */
  private viewItemDetail() {
    if (this.notification && this.notification.type === NotificationType.EXPIRY && this.notification.relatedId) {
      // 返回到Home页面并打开物品详情
      router.pushUrl({
        url: 'pages/Home',
        params: {
          openItemDetail: true,
          itemId: this.notification.relatedId
        }
      });
    } else {
      promptAction.showToast({ message: '没有关联的物品详情' });
    }
  }
  
  /**
   * 删除通知
   */
  private deleteNotification() {
    if (this.notification) {
      const buttons = [
        { text: '取消', color: this.isDarkMode ? '#AAAAAA' : '#666666' },
        { text: '确定', color: '#E53935' }
      ];
      
      promptAction.showDialog({
        title: '删除通知',
        message: '确定要删除这条通知吗？',
        buttons: buttons
      }).then(result => {
        if (result.index === 1) {
          // 删除通知
          const success = this.notificationManager.deleteNotification(this.notification.id);
          if (success) {
            promptAction.showToast({ message: '通知已删除' });
            // 返回上一页
            this.goBack();
          } else {
            promptAction.showToast({ message: '删除通知失败' });
          }
        }
      }).catch(err => {
        console.error('显示对话框失败', err);
      });
    }
  }
  
  /**
   * 获取通知类型图标
   */
  private getNotificationTypeIcon(): ResourceStr {
    if (!this.notification) {
      return $r('app.media.notification_bell');
    }
    
    switch (this.notification.type) {
      case NotificationType.EXPIRY:
        return $r('app.media.notification_bell');
      case NotificationType.UPDATE:
        return $r('app.media.notification_bell');
      case NotificationType.SYSTEM:
      default:
        return $r('app.media.notification_bell');
    }
  }
  
  /**
   * 获取通知类型颜色
   */
  private getNotificationTypeColor(): string {
    if (!this.notification) {
      return '#52C41A';
    }
    
    switch (this.notification.type) {
      case NotificationType.EXPIRY:
        return '#FF4D4F';
      case NotificationType.UPDATE:
        return '#1890FF';
      case NotificationType.SYSTEM:
      default:
        return '#52C41A';
    }
  }
  
  /**
   * 获取通知类型名称
   */
  private getNotificationTypeName(): string {
    if (!this.notification) {
      return '系统通知';
    }
    
    switch (this.notification.type) {
      case NotificationType.EXPIRY:
        return '过期提醒';
      case NotificationType.UPDATE:
        return '功能更新';
      case NotificationType.SYSTEM:
      default:
        return '系统通知';
    }
  }
  
  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.back'))
          .width(24)
          .height(24)
          .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
          .onClick(() => {
            this.goBack();
          })
        Text('通知详情')
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .fontColor(this.colors.PRIMARY_TEXT)
          .margin({ left: 8 })
          
        Blank()
        
        Button('删除')
          .fontSize(14)
          .fontColor(this.colors.PRIMARY_COLOR)
          .backgroundColor(Color.Transparent)
          .onClick(() => this.deleteNotification())
      }
      .width('100%')
      .height(50)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(this.colors.CARD_BACKGROUND)
      
      // 通知详情内容
      if (this.notification) {
        Column() {
          // 通知标题和类型
          Row() {
            // 通知类型标签
            Text(this.getNotificationTypeName())
              .fontSize(12)
              .fontWeight(FontWeight.Medium)
              .fontColor('#FFFFFF')
              .backgroundColor(this.getNotificationTypeColor())
              .borderRadius(10)
              .padding({ left: 8, right: 8, top: 2, bottom: 2 })
              .margin({ right: 8 })
            
            // 通知标题
            Text(this.notification.title)
              .fontSize(18)
              .fontWeight(FontWeight.Bold)
              .fontColor(this.colors.PRIMARY_TEXT)
          }
          .width('100%')
          .padding({ top: 16, bottom: 8 })
          
          // 通知时间
          Text(this.notification.time)
            .fontSize(14)
            .fontColor(this.colors.HINT_TEXT)
            .margin({ bottom: 16 })
          
          // 通知内容
          Text(this.notification.detailContent || this.notification.content)
            .fontSize(16)
            .fontColor(this.colors.SECONDARY_TEXT)
            .width('100%')
            .margin({ bottom: 24 })
          
          // 操作按钮区域
          if (this.notification.type === NotificationType.EXPIRY && this.notification.relatedId) {
            Button('查看物品详情')
              .width('80%')
              .height(40)
              .fontSize(16)
              .fontWeight(FontWeight.Medium)
              .fontColor(Color.White)
              .backgroundColor(this.getNotificationTypeColor())
              .borderRadius(20)
              .margin({ top: 16 })
              .onClick(() => this.viewItemDetail())
          }
        }
        .width('100%')
        .padding({ left: 16, right: 16 })
      } else {
        // 通知不存在时显示空状态
        Column() {
          Image($r('app.media.notification_bell'))
            .width(80)
            .height(80)
            .fillColor(this.colors.HINT_TEXT)
            .opacity(0.5)
            .margin({ bottom: 16 })
          
          Text('通知不存在或已被删除')
            .fontSize(16)
            .fontColor(this.colors.HINT_TEXT)
        }
        .width('100%')
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
      }
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
  }

  /**
   * 页面转场动画
   */
  pageTransition() {
    // Push操作时页面进入的动画：从右侧滑入
    PageTransitionEnter({ type: RouteType.Push, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Right)
    
    // Pop操作时页面进入的动画：从左侧滑入
    PageTransitionEnter({ type: RouteType.Pop, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Left)
    
    // Push操作时页面退出的动画：向左侧滑出
    PageTransitionExit({ type: RouteType.Push, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Left)
    
    // Pop操作时页面退出的动画：向右侧滑出
    PageTransitionExit({ type: RouteType.Pop, duration: 300, curve: Curve.EaseOut })
      .slide(SlideEffect.Right)
  }
} 