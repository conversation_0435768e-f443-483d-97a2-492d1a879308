/**
 * 数据库常量定义
 */
export class DBConstants {
  // 数据库名
  static readonly DB_NAME: string = 'storage_box.db';
  // 数据库版本
  static readonly DB_VERSION: number = 4;

  // 物品表
  static readonly ITEM_TABLE: string = 'item';
  // 物品表列名
  static readonly COLUMN_ID: string = 'id';
  static readonly COLUMN_NAME: string = 'name';
  static readonly COLUMN_LOCATION: string = 'location';
  static readonly COLUMN_IMAGE: string = 'image';
  static readonly COLUMN_EXPIRY_DAYS: string = 'expiry_days';
  static readonly COLUMN_EXPIRY_UNIT: string = 'expiry_unit';
  static readonly COLUMN_ITEM_TYPE: string = 'item_type';
  static readonly COLUMN_PRICE: string = 'price';
  static readonly COLUMN_PURCHASE_DATE: string = 'purchase_date';
  static readonly COLUMN_PRODUCTION_DATE: string = 'production_date';
  static readonly COLUMN_CREATE_TIME: string = 'create_time';
  static readonly COLUMN_UPDATE_TIME: string = 'update_time';

  // SQL语句：创建物品表
  static readonly CREATE_ITEM_TABLE: string = `
    CREATE TABLE IF NOT EXISTS ${DBConstants.ITEM_TABLE} (
      ${DBConstants.COLUMN_ID} INTEGER PRIMARY KEY AUTOINCREMENT,
      ${DBConstants.COLUMN_NAME} TEXT NOT NULL,
      ${DBConstants.COLUMN_LOCATION} TEXT NOT NULL,
      ${DBConstants.COLUMN_IMAGE} TEXT,
      ${DBConstants.COLUMN_EXPIRY_DAYS} INTEGER DEFAULT 0,
      ${DBConstants.COLUMN_EXPIRY_UNIT} TEXT DEFAULT 'month',
      ${DBConstants.COLUMN_ITEM_TYPE} INTEGER DEFAULT 6,
      ${DBConstants.COLUMN_PRICE} REAL DEFAULT 0.0,
      ${DBConstants.COLUMN_PURCHASE_DATE} TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ${DBConstants.COLUMN_PRODUCTION_DATE} TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ${DBConstants.COLUMN_CREATE_TIME} TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      ${DBConstants.COLUMN_UPDATE_TIME} TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
  `;
  
  // SQL语句：添加生产日期字段（用于数据库迁移）
  static readonly ADD_PRODUCTION_DATE_COLUMN: string = `
    ALTER TABLE ${DBConstants.ITEM_TABLE} 
    ADD COLUMN ${DBConstants.COLUMN_PRODUCTION_DATE} TIMESTAMP DEFAULT CURRENT_TIMESTAMP
  `;
} 