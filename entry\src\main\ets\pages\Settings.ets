import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';
import { UserManager } from '../common/utils/UserManager';

/**
 * 设置页面
 */
@Entry
@Component
struct Settings {
  @State userName: string = 'XXX';
  @State editUserName: boolean = false;
  @State tempUserName: string = '';
  @State phoneNumber: string = '138****1234';
  @State currentTheme: string = ThemeConstants.THEME_AUTO;
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  // 用户管理相关
  private userManager: UserManager = UserManager.getInstance();
  private usernameChangeCallback = (username: string) => {
    this.userName = username;
  };

  aboutToAppear() {
    // 获取路由参数
    const params = router.getParams() as Record<string, string>;
    if (params && params.userName) {
      this.userName = params.userName;
    }
    
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      // 加载当前主题设置
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
    
    // 初始化用户管理器
    this.userManager.init().then(() => {
      // 加载用户名
      this.loadUsername();
    });
    
    // 注册用户名变化回调
    this.userManager.registerUsernameChangeCallback(this.usernameChangeCallback);
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
    
    // 移除用户名变化回调
    this.userManager.unregisterUsernameChangeCallback(this.usernameChangeCallback);
  }
  
  /**
   * 加载用户名
   */
  async loadUsername() {
    try {
      // 获取当前用户名
      const username = await this.userManager.getUsername();
      this.userName = username;
      console.info('加载用户名', username);
    } catch (err) {
      console.error('加载用户名失败', err);
    }
  }
  
  /**
   * 更新用户名
   */
  async updateUsername() {
    const trimmedName = this.tempUserName.trim();
    if (trimmedName !== '') {
      // 检查用户名长度是否超过10个字符
      if (trimmedName.length > 10) {
        promptAction.showToast({ message: '用户名不能超过10个字符' });
        return; // 如果超过限制，不保存并返回
      }
      
      // 保存到共享偏好
      try {
        await this.userManager.setUsername(trimmedName);
        this.userName = trimmedName;
        promptAction.showToast({ message: '用户名已更新' });
      } catch (err) {
        console.error('保存用户名失败', err);
        promptAction.showToast({ message: '用户名更新失败' });
      }
    }
    this.editUserName = false;
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前主题设置
      this.currentTheme = await this.themeManager.getTheme();
      
      // 获取当前是否为深色模式
      this.isDarkMode = await this.themeManager.isDarkMode();
      
      // 更新主题颜色
      this.colors = this.isDarkMode ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
      
      console.info('加载主题设置', '当前主题:', this.currentTheme, '是否暗黑模式:', this.isDarkMode);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('Settings页面主题更新', isDark ? '深色模式' : '浅色模式');
  }
  
  /**
   * 切换主题设置
   */
  async toggleTheme(isOn: boolean) {
    try {
      // 设置新的主题
      const newTheme = isOn ? ThemeConstants.THEME_DARK : ThemeConstants.THEME_LIGHT;
      await this.themeManager.setTheme(newTheme);
      this.currentTheme = newTheme;
      
      // 更新暗黑模式状态
      this.isDarkMode = await this.themeManager.isDarkMode();
      this.updateTheme(this.isDarkMode);
      
      // 提示用户
      const message = isOn ? '已切换到深色模式' : '已切换到浅色模式';
      promptAction.showToast({ message });
      
    } catch (err) {
      console.error('切换主题失败', err);
    }
  }

  /**
   * 前往修改密码页面
   */
  gotoChangePassword() {
    try {
      console.info('准备跳转到修改密码页面');
      router.pushUrl({
        url: 'pages/ResetPassword',
        params: {
          title: '修改密码',
          actionText: '确认修改'
        }
      }).then(() => {
        console.info('成功跳转到修改密码页面');
      }).catch((err: Error) => {
        console.error(`跳转到修改密码页面失败: ${err.message}`);
        promptAction.showToast({ message: `跳转失败: ${err.message}` });
      });
    } catch (error) {
      console.error(`跳转出错: ${error.message}`);
      promptAction.showToast({ message: `跳转出错: ${error.message}` });
    }
  }

  /**
   * 前往用户协议页面
   */
  gotoUserAgreement() {
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '用户协议',
        url: 'https://example.com/user-agreement'
      }
    });
  }

  /**
   * 前往隐私政策页面
   */
  gotoPrivacyPolicy() {
    router.pushUrl({
      url: 'pages/WebView',
      params: {
        title: '隐私政策',
        url: 'https://example.com/privacy-policy'
      }
    });
  }

  build() {
    Column() {
      // 顶部导航栏
      Row() {
        Image($r('app.media.menu_icon'))
          .width(24)
          .height(24)
          .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
          .onClick(() => {
            router.back();
          })
        Text('设置')
          .fontSize(20)
          .fontWeight(FontWeight.Medium)
          .fontColor(this.colors.PRIMARY_TEXT)
          .margin({ left: 8 })
      }
      .width('100%')
      .height(50)
      .padding({ left: 16, right: 16 })
      .alignItems(VerticalAlign.Center)
      .backgroundColor(this.colors.CARD_BACKGROUND)

      // 设置项列表
      List() {
        // 手机号
        ListItem() {
          Row() {
            Text('手机号')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Text(this.phoneNumber)
              .fontSize(14)
              .fontColor(this.colors.HINT_TEXT)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            promptAction.showToast({ message: '点击了手机号设置' });
          })
        }
        
        // 用户名
        ListItem() {
          Row() {
            Text('用户名')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            if (this.editUserName) {
              Row() {
                TextInput({ 
                  text: this.tempUserName,
                  placeholder: '请输入用户名'
                })
                  .width('60%')
                  .height(36)
                  .fontSize(14)
                  .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
                  .borderRadius(4)
                  .padding({ left: 8, right: 8 })
                  .fontColor(this.colors.PRIMARY_TEXT)
                  .placeholderColor(this.colors.HINT_TEXT)
                  .maxLength(10) // 限制最大输入长度为10个字符
                  .onChange((value) => {
                    this.tempUserName = value;
                  })
                
                Button('保存')
                  .height(32)
                  .fontSize(14)
                  .margin({ left: 8 })
                  .onClick(() => {
                    this.updateUsername();
                  })
              }
            } else {
              Row() {
                Text(this.userName)
                  .fontSize(14)
                  .fontColor(this.colors.HINT_TEXT)
                
                Image($r('app.media.edit'))
                  .width(16)
                  .height(16)
                  .margin({ left: 8 })
                  .fillColor(this.colors.HINT_TEXT)
              }
            }
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            if (!this.editUserName) {
              this.tempUserName = this.userName;
              this.editUserName = true;
            }
          })
        }
        
        // 修改密码
        ListItem() {
          Row() {
            Text('修改密码')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Text('') // 空文本占位，保持样式一致
              .fontSize(14)
              .fontColor(this.colors.HINT_TEXT)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            this.gotoChangePassword();
          })
        }
        
        // 暗黑模式设置
        ListItem() {
          Row() {
            Text('暗黑模式')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Toggle({ type: ToggleType.Switch, isOn: this.isDarkMode })
              .width(36)
              .height(20)
              .onChange((isOn) => {
                this.toggleTheme(isOn);
              })
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
        }
        
        // 关于
        ListItem() {
          Row() {
            Text('关于')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Text('版本 1.0.0')
              .fontSize(14)
              .fontColor(this.colors.HINT_TEXT)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            promptAction.showToast({ message: '点击了关于' });
          })
        }
        
        // 用户协议
        ListItem() {
          Row() {
            Text('用户协议')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Text('') // 空文本占位，保持样式一致
              .fontSize(14)
              .fontColor(this.colors.HINT_TEXT)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            this.gotoUserAgreement();
          })
        }
        
        // 隐私政策
        ListItem() {
          Row() {
            Text('隐私政策')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
            
            Text('') // 空文本占位，保持样式一致
              .fontSize(14)
              .fontColor(this.colors.HINT_TEXT)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .padding(16)
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            this.gotoPrivacyPolicy();
          })
        }
      }
      .width('100%')
      .margin({ top: 16 })
      .divider({ strokeWidth: 1, color: this.colors.DIVIDER_COLOR })
    }
    .width('100%')
    .height('100%')
    .backgroundColor(this.colors.PAGE_BACKGROUND)
  }
} 