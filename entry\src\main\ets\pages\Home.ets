import router from '@ohos.router';
import promptAction from '@ohos.promptAction';
import inputMethod from '@ohos.inputMethod';
import { common } from '@kit.AbilityKit';
import { abilityAccessCtrl, bundleManager, Permissions } from '@kit.AbilityKit';
import { MedicineItem } from '../model/MedicineItem';
import { ItemType, getItemTypeIcon, getItemTypeName, ALL_ITEM_TYPES, addCustomType, getAllItemTypes } from '../model/ItemType';
import { ThemeConstants, ThemeColors } from '../common/constants/ThemeConstants';
import { ThemeManager } from '../common/utils/ThemeManager';
import { UserManager } from '../common/utils/UserManager';
import { DBManager } from '../database/DBManager';
import { NotificationManager, Item } from '../common/utils/NotificationManager';
import { HuaweiSpeechRecognizer } from '../common/utils/HuaweiSpeechRecognizer';

// 获取上下文的辅助函数
function getContext(component: ESObject): common.UIAbilityContext {
  return (component as ESObject).getUIContext().getHostContext() as common.UIAbilityContext;
}

// 获取华为语音识别器实例
const huaweiSpeechRecognizer = HuaweiSpeechRecognizer.getInstance();

// 定义语音输入解析结果接口
interface VoiceInputResult {
  name?: string;
  type?: ItemType;
  location?: string;
  quantity?: number;
  description?: string;
  price?: number;
}

// 自定义路由错误接口
class RouterError extends Error {
  code: number = 0;
}

// 自定义路由参数接口
interface RouterParams {
  unreadNotifications?: number;
  openItemDetail?: boolean;
  itemName?: string;
  itemLocation?: string;
}

// 定义DatePickerValue接口
interface DatePickerValue {
  year?: number;
  month?: number;  
  day?: number;
}

/**
 * Home页面
 * 显示用户物品信息和库存
 */
@Entry
@Component
struct Home {
  @State totalAssets: number = 0; // 改为数字类型，以便进行计算
  @State totalCount: number = 0;
  @State searchText: string = ''; // 搜索文本
  @State isSearching: boolean = false; // 是否正在搜索
  @State showDetailDialog: boolean = false; // 是否显示详情对话框
  @State selectedItem: MedicineItem = new MedicineItem('', '', $r('app.media.add'), 0, ItemType.GENERAL, 0); // 选中的物品
  @State dialogHeight: number = 420; // 对话框高度
  @State isEditMode: boolean = false; // 是否处于编辑模式
  @State expiryDate: string = ''; // 保质期至日期
  @State dynamicExpiryDays: number = 0; // 动态计算的过期天数
  @State userName: string = 'XXX'; // 用户名
  @State selectedDate: Date = new Date(); // 选择的日期
  @State unreadNotifications: number = 2; // 未读通知数量
  
  // 主题相关状态
  @State isDarkMode: boolean = false;
  @State colors: ThemeColors = ThemeConstants.LIGHT_COLORS;
  private themeManager: ThemeManager = ThemeManager.getInstance();
  private themeChangeCallback = (isDark: boolean) => {
    this.updateTheme(isDark);
  };
  
  // 用户管理相关
  private userManager: UserManager = UserManager.getInstance();
  private usernameChangeCallback = (username: string) => {
    this.userName = username;
    // 用户名变化时更新登录状态
    this.checkLoginStatus();
  };
  
  // 语音输入相关状态
  @State showVoiceDialog: boolean = false; // 是否显示语音输入弹窗
  @State voiceInputText: string = ''; // 语音输入的文本
  @State voiceExampleText: string = '示例：请说"添加一个苹果到客厅茶几"'; // 语音案例文本
  @State isRecording: boolean = false; // 是否正在录音
  @State voiceDialogTranslateY: number = 430; // 语音弹窗Y轴偏移量
  @State voiceDialogOpacity: number = 0; // 语音弹窗透明度
  @State recordingTime: number = 0; // 录音计时器
  @State recordingTimer: number = -1; // 计时器ID
  
  // 语音识别相关状态
  @State isRecognizing: boolean = false; // 是否正在识别
  @State recognitionError: string = ''; // 识别错误信息
  @State realtimeText: string = ''; // 实时识别文本
  @State recognitionStatus: string = ''; // 识别状态提示
  
  // 华为官方语音识别服务
  private isRecognitionInitialized: boolean = false;
  
  // 编辑状态的临时变量
  @State editName: string = '';
  @State editLocation: string = '';
  @State editPrice: string = '20.00';
  @State editExpiryMonths: string = '12';
  @State editExpiryDays: string = '365';
  @State expiryUnitIsMonth: boolean = true; // 保质期单位是否为月
  @State editProductionDate: string = '';
  @State editPurchaseDate: string = ''; // 购买日期
  @State showAddMenu: boolean = false; // 是否显示添加菜单
  @State isAddingNewItem: boolean = false; // 是否正在添加新物品
  
  // 动画状态
  @State dialogTranslateY: number = 0; // Y轴偏移量
  @State maskOpacity: number = 0; // 遮罩透明度
  @State menuOpacity: number = 0; // 添加菜单透明度
  @State menuTranslateY: number = 0; // 添加菜单Y轴偏移量
  
  // 侧边菜单相关状态
  @State showSideMenu: boolean = false; // 是否显示侧边菜单
  @State sideMenuTranslateX: number = -280; // 侧边菜单X轴偏移量
  @State sideMenuMaskOpacity: number = 0; // 侧边菜单遮罩透明度
  @State editUserName: boolean = false; // 是否正在编辑用户名
  @State tempUserName: string = ''; // 临时存储编辑中的用户名
  @State isLoggedIn: boolean = false; // 用户是否已登录
  
  // 位置选择器相关变量
  @State showLocationPicker: boolean = false; // 是否显示位置选择器
  // 位置选择器数据结构 - 改为关联性结构
  @State locationMap: Record<string, string[]> = {
    '客厅': ['茶几', '电视柜', '沙发', '地柜', '装饰柜', '其他'],
    '卧室': ['床头柜', '衣柜', '梳妆台', '床下', '床上', '其他'],
    '厨房': ['冰箱', '橱柜', '碗柜', '调料架', '水槽', '其他'],
    '书房': ['书架', '书桌', '抽屉', '文件柜', '其他'],
    '浴室': ['洗漱台', '浴室柜', '置物架', '其他'],
    '阳台': ['晾衣架', '储物箱', '花盆架', '其他'],
    '其他': ['其他']
  };
  
  // 位置词条编辑相关
  @State showLocationEditDialog: boolean = false; // 是否显示位置词条编辑对话框
  @State editingLocationLevel: number = 0; // 当前编辑的位置层级
  @State editingLocationItem: string = ''; // 当前编辑的位置词条
  @State editingLocationNewValue: string = ''; // 编辑后的新词条值
  
  // 第三级位置选项 - 所有区域通用
  @State thirdLevelOptions: string[] = ['上层', '中层', '下层', '药盒', '药箱', '左侧', '右侧', '其他'];
  
  // 当前可选的位置选项
  @State locationLevels: string[][] = [
    Object.keys(this.locationMap), // 第一级为所有区域
    [], // 第二级初始为空，将根据选择的第一级动态填充
    this.thirdLevelOptions // 第三级为通用选项
  ];
  @State selectedLocation: string[] = ['客厅', '茶几', '上层']; // 默认选中的位置
  @State tempLocation: string[] = []; // 临时保存编辑中的位置
  @State customCategory: string = ''; // 自定义类别输入
  @State showCustomInput: boolean = false; // 是否显示自定义输入框
  @State currentPickerLevel: number = 0; // 当前正在编辑的位置层级
  @State showTypeSelector: boolean = false; // 是否显示类型选择器
  @State selectedItemType: ItemType = ItemType.GENERAL; // 当前选择的物品类型
  
  // 物品类型选择器相关变量
  @State showAddTypeInput: boolean = false; // 是否显示添加类型输入框
  @State newTypeName: string = ''; // 新类型名称
  @State itemTypes: ItemType[] = getAllItemTypes(); // 所有物品类型，包括自定义类型
  
  // 搜索气泡与筛选相关状态
  @State showSearchBubbles: boolean = false; // 是否显示搜索气泡
  @State selectedCategoryFilter: ItemType | undefined = undefined; // 选中的类别筛选
  @State selectedLocationFilter: string = ''; // 选中的位置筛选（一级位置）

  // 数据库管理器
  private dbManager: DBManager = DBManager.getInstance();

  // 通知管理相关
  private notificationManager: NotificationManager = NotificationManager.getInstance();
  private unreadCountCallback = (count: number) => {
    this.unreadNotifications = count;
  };

  /**
   * 隐藏搜索相关界面
   */
  hideSearchInterface() {
    this.showSearchBubbles = false;
    // 隐藏软键盘
    try {
      let inputMethodController = inputMethod.getController();
      inputMethodController.stopInputSession();
    } catch (err) {
      console.error('隐藏软键盘失败:', err);
    }
  }

  /**
   * 计算总资产
   */
  calculateTotalAssets() {
    // 计算所有物品价格的总和
    let total = 0;
    this.medicineList.forEach(item => {
      total += item.price;
    });
    
    // 更新总资产
    this.totalAssets = total;
  }

  aboutToAppear() {
    // 初始化主题管理器
    this.themeManager.init().then(() => {
      this.loadThemeSettings();
    });
    
    // 注册主题变化回调
    this.themeManager.registerThemeChangeCallback(this.themeChangeCallback);
    
    // 初始化数据库
    this.initDatabase();
    
    // 注册未读通知数量回调
    this.notificationManager.registerUnreadCountCallback('Home', (count) => {
      this.unreadNotifications = count;
    });
    
    // 获取当前未读通知数量（确保数据已初始化）
    this.notificationManager.getNotifications(); // 先初始化数据
    this.unreadNotifications = this.notificationManager.getUnreadCount();
    
    // 检查过期物品并生成通知
    this.checkExpiryItems();

    // 初始化华为官方语音识别器
    this.initSpeechRecognizer().catch((error: Error) => {
      console.error('华为语音识别器初始化失败:', error);
      // 不阻止应用启动，只是语音功能不可用
    });
  }
  
  /**
   * 检查路由参数
   */
  private checkRouterParams() {
    try {
      const params = router.getParams() as RouterParams;
      if (params) {
        // 更新未读通知数量
        if (params.unreadNotifications !== undefined) {
          this.unreadNotifications = params.unreadNotifications;
          console.info('从通知页面返回，更新未读通知数量:', this.unreadNotifications);
        }
        
        // 处理从通知页面跳转来的物品详情显示
        if (params.openItemDetail && params.itemName && params.itemLocation) {
          // 查找对应的物品
          const item = this.medicineList.find(medicine => 
            medicine.name === params.itemName && 
            medicine.location === params.itemLocation
          );
          
          if (item) {
            this.selectedItem = item;
            this.showDetailDialog = true;
            console.info('显示物品详情:', item);
          } else {
            promptAction.showToast({ message: '未找到对应的物品' });
          }
        }
      }
    } catch (error) {
      console.error('获取路由参数失败', error);
    }
  }
  
  /**
   * 在页面重新显示时检查参数
   */
  onPageShow() {
    // 从通知管理器获取最新的未读通知数量
    this.unreadNotifications = this.notificationManager.getUnreadCount();
    console.info('页面重新显示，更新未读通知数量:', this.unreadNotifications);
  }
  
  aboutToDisappear() {
    // 移除主题变化回调
    this.themeManager.unregisterThemeChangeCallback(this.themeChangeCallback);
    
    // 移除用户名变化回调
    this.userManager.unregisterUsernameChangeCallback(this.usernameChangeCallback);
    
    // 移除未读数量变化回调
    this.notificationManager.unregisterUnreadCountCallback('Home');
    
    // 释放语音识别器资源
    this.releaseSpeechRecognizer();
  }
  
  /**
   * 加载用户名
   */
  async loadUsername() {
    try {
      // 获取当前用户名
      const username = await this.userManager.getUsername();
      this.userName = username;
      console.info('加载用户名', username);
      // 检查登录状态
      this.checkLoginStatus();
    } catch (err) {
      console.error('加载用户名失败', err);
    }
  }
  
  /**
   * 检查用户是否已登录
   * @returns 用户是否已登录
   */
  private checkLoginStatus(): boolean {
    // 通过检查用户名是否为空或默认值来判断用户是否登录
    const isLoggedIn = this.userName !== '' && this.userName !== 'XXX' && this.userName !== '未登录';
    this.isLoggedIn = isLoggedIn;
    return isLoggedIn;
  }
  
  /**
   * 加载主题设置
   */
  async loadThemeSettings() {
    try {
      // 获取当前是否为深色模式
      const isDark = await this.themeManager.isDarkMode();
      this.updateTheme(isDark);
    } catch (err) {
      console.error('加载主题设置失败', err);
    }
  }
  
  /**
   * 更新主题
   * @param isDark 是否为深色模式
   */
  private updateTheme(isDark: boolean) {
    this.isDarkMode = isDark;
    this.colors = isDark ? ThemeConstants.DARK_COLORS : ThemeConstants.LIGHT_COLORS;
    console.info('Home页面主题更新', isDark ? '深色模式' : '浅色模式');
  }

  @State dailyExpense: number = 238;
  @State currentTab: number = 0; // 当前选中的标签页
  @State medicineList: Array<MedicineItem> = []; // 初始化为空数组

  /**
   * 打开侧边菜单
   */
  openSideMenu() {
    // 打开侧边菜单前，收起搜索气泡
    if (this.showSearchBubbles) {
      this.showSearchBubbles = false;
    }
    this.showSideMenu = true;
    this.tempUserName = this.userName;
    
    // 添加动画效果
    animateTo({
      duration: 300,
      curve: Curve.EaseOut,
      iterations: 1,
      playMode: PlayMode.Normal
    }, () => {
      this.sideMenuTranslateX = 0;
      this.sideMenuMaskOpacity = 0.5;
    });
  }
  
  /**
   * 关闭侧边菜单
   */
  closeSideMenu() {
    // 如果正在编辑用户名，先保存用户名
    if (this.editUserName) {
      this.saveUserName();
    }
    
    // 添加动画效果
    animateTo({
      duration: 300,
      curve: Curve.EaseOut,
      iterations: 1,
      playMode: PlayMode.Normal
    }, () => {
      this.sideMenuTranslateX = -280;
      this.sideMenuMaskOpacity = 0;
    });
    
    // 延迟隐藏侧边菜单，等待动画完成
    setTimeout(() => {
      this.showSideMenu = false;
      this.editUserName = false;
    }, 300);
  }
  
  /**
   * 保存用户名
   */
  async saveUserName() {
    const trimmedName = this.tempUserName.trim();
    if (trimmedName !== '') {
      // 检查用户名长度是否超过10个字符
      if (trimmedName.length > 10) {
        promptAction.showToast({ message: '用户名不能超过10个字符' });
        return; // 如果超过限制，不保存并返回
      }
      
      // 更新本地状态
      this.userName = trimmedName;
      
      // 保存到共享偏好
      try {
        await this.userManager.setUsername(trimmedName);
        promptAction.showToast({ message: '用户名已更新' });
      } catch (err) {
        console.error('保存用户名失败', err);
        promptAction.showToast({ message: '用户名更新失败' });
      }
    }
    this.editUserName = false;
  }
  
  build() {
    Stack({ alignContent: Alignment.Bottom }) {
      Column() {
        // 顶部导航栏
        Row() {
          Row() {
            Image($r('app.media.menu_icon'))
              .width(24)
              .height(24)
              .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
              .onClick(() => {
                this.openSideMenu();
              })
            Text('物品管家')
              .fontSize(20)
              .fontWeight(FontWeight.Medium)
              .fontColor(this.colors.PRIMARY_TEXT)
              .margin({ left: 8 })
          }
          .width('100%')
          .justifyContent(FlexAlign.Start)
        }
        .width('100%')
        .height(50)
        .padding({ left: 16, right: 16 })
        .alignItems(VerticalAlign.Center)
        .backgroundColor(this.colors.CARD_BACKGROUND)
        .onClick(() => {
          // 点击导航栏时隐藏搜索界面
          this.hideSearchInterface();
        })

        // 搜索框和添加按钮
        Column() {
          Row() {
            Row() {
              Image($r('app.media.search_icon'))
                .width(20)
                .height(20)
                .margin({ left: 12, right: 8 })
                .fillColor(this.isDarkMode ? '#AAAAAA' : '#999999')
              
              TextInput({ 
                placeholder: '搜索物品...',
                text: this.searchText 
              })
                .width('100%')
                .height(36)
                .backgroundColor(Color.Transparent)
                .placeholderColor(this.isDarkMode ? '#999999' : '#999999')
                .fontColor(this.isDarkMode ? '#FFFFFF' : '#333333')
                .fontSize(16)
                .padding({ left: 0, right: 8 })
                .onFocus(() => {
                  this.showSearchBubbles = true;
                })
                .onBlur(() => {
                  this.showSearchBubbles = false;
                })
                .onChange((value: string) => {
                  this.searchText = value;
                  this.searchItems();
                })
            }
            .width('80%')
            .height(44)
            .borderRadius(22)
            .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
            .alignItems(VerticalAlign.Center)

            // 添加按钮
            Button({ type: ButtonType.Circle }) {
              Image($r('app.media.add'))
                .width(24)
                .height(24)
                .fillColor('#FFFFFF')
            }
            .width(44)
            .height(44)
            .backgroundColor(this.colors.PRIMARY_COLOR)
            .margin({ left: 12 })
            .onClick(() => {
              // 点击添加按钮打开或关闭下拉菜单
              if (!this.showAddMenu) {
                this.openAddMenu();
              } else {
                this.closeAddMenu();
              }
            })
          }
          .width('100%')
          .padding({ left: 16, right: 16 })
          .margin({ top: 10, bottom: 10 })
          .justifyContent(FlexAlign.SpaceBetween)

          // 搜索建议气泡：第一行类别，第二行位置
          if (this.showSearchBubbles) {
            Column() {
              // 第一行：类别气泡
              Scroll() {
                Row() {
                  // 类别全选（全部）
                  Button() {
                    Row() {
                      Image($r('app.media.icon_general_color'))
                        .width(16)
                        .height(16)
                        .margin({ right: 4 })
                      Text('全部')
                        .fontSize(14)
                    }
                  }
                  .height(28)
                  .padding({ left: 10, right: 10 })
                  .margin({ right: 8, top: 8 })
                  .backgroundColor(this.selectedCategoryFilter === undefined ? this.colors.PRIMARY_COLOR : (this.isDarkMode ? '#2E2E2E' : '#EDEDED'))
                  .fontColor(this.selectedCategoryFilter === undefined ? '#FFFFFF' : (this.isDarkMode ? '#DDDDDD' : '#333333'))
                  .borderRadius(14)
                  .onClick(() => {
                    this.selectedCategoryFilter = undefined;
                    this.isSearching = true;
                  })

                  // 其余类别按钮
                  ForEach(this.itemTypes, (tp: ItemType) => {
                    Button() {
                      Row() {
                        Image(getItemTypeIcon(tp))
                          .width(16)
                          .height(16)
                          .margin({ right: 4 })
                        Text(getItemTypeName(tp))
                          .fontSize(14)
                      }
                    }
                      .height(28)
                      .padding({ left: 10, right: 10 })
                      .margin({ right: 8, top: 8 })
                      .backgroundColor(this.selectedCategoryFilter === tp ? this.colors.PRIMARY_COLOR : (this.isDarkMode ? '#2E2E2E' : '#EDEDED'))
                      .fontColor(this.selectedCategoryFilter === tp ? '#FFFFFF' : (this.isDarkMode ? '#DDDDDD' : '#333333'))
                      .borderRadius(14)
                      .onClick(() => {
                        this.selectedCategoryFilter = (this.selectedCategoryFilter === tp) ? undefined : tp;
                        this.isSearching = true;
                      })
                  })
                }
                .padding({ left: 16, right: 16 })
              }
              .scrollable(ScrollDirection.Horizontal)
              .scrollBar(BarState.Off)
              .width('100%')

              // 第二行：位置气泡（只展示一级位置）
              Scroll() {
                Row() {
                  // 位置全选（全部）
                  Button() {
                    Text('全部')
                      .fontSize(14)
                  }
                    .height(28)
                    .padding({ left: 10, right: 10 })
                    .margin({ right: 8, top: 6, bottom: 8 })
                    .backgroundColor(this.selectedLocationFilter === '' ? this.colors.PRIMARY_COLOR : (this.isDarkMode ? '#2E2E2E' : '#EDEDED'))
                    .fontColor(this.selectedLocationFilter === '' ? '#FFFFFF' : (this.isDarkMode ? '#DDDDDD' : '#333333'))
                    .borderRadius(14)
                    .onClick(() => {
                      this.selectedLocationFilter = '';
                      this.isSearching = true;
                    })
                  ForEach(Object.keys(this.locationMap), (loc: string) => {
                    Button() {
                      Text(loc)
                        .fontSize(14)
                    }
                      .height(28)
                      .padding({ left: 10, right: 10 })
                      .margin({ right: 8, top: 6, bottom: 8 })
                      .backgroundColor(this.selectedLocationFilter === loc ? this.colors.PRIMARY_COLOR : (this.isDarkMode ? '#2E2E2E' : '#EDEDED'))
                      .fontColor(this.selectedLocationFilter === loc ? '#FFFFFF' : (this.isDarkMode ? '#DDDDDD' : '#333333'))
                      .borderRadius(14)
                      .onClick(() => {
                        this.selectedLocationFilter = (this.selectedLocationFilter === loc) ? '' : loc;
                        this.isSearching = true;
                      })
                  })
                }
                .padding({ left: 16, right: 16 })
              }
              .scrollable(ScrollDirection.Horizontal)
              .scrollBar(BarState.Off)
              .width('100%')
            }
            .width('100%')
            .backgroundColor(this.colors.CARD_BACKGROUND)
          }
        }
        .width('100%')
        .margin({ top: 10, bottom: 10 })
        .backgroundColor(this.colors.CARD_BACKGROUND)

        // 资产信息卡片
        Row() {
          Column() {
            Row() {
              Text(`${this.userName}的资产`)
                .fontSize(16)
                .fontColor(this.colors.PRIMARY_TEXT)
                .margin({ bottom: 8 })
            }
            Text(`¥ ${this.totalAssets.toFixed(2)}`)
              .fontSize(28)
              .fontWeight(FontWeight.Bold)
              .fontColor(this.colors.PRIMARY_TEXT)
            Blank()
              .height(16)
            Row() {
              Text(`总件数：${this.totalCount}`)
                .fontSize(14)
                .fontColor(this.colors.SECONDARY_TEXT)
                .opacity(0.7)
            }
          }
          .alignItems(HorizontalAlign.Start)
          .layoutWeight(1)
          .justifyContent(FlexAlign.SpaceBetween)

          Column() {
            Image($r('app.media.plant_box'))
              .width(80)
              .height(80)
              .objectFit(ImageFit.Contain)
            Blank()
              .height(16)
            Row() {
              Text('日均：')
                .fontSize(14)
                .fontColor(this.colors.SECONDARY_TEXT)
                .opacity(0.7)
              Text(`¥ ${this.dailyExpense}`)
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
                .fontColor(this.colors.PRIMARY_TEXT)
                .margin({ left: 4 })
            }
          }
          .alignItems(HorizontalAlign.Center)
        }
        .width('100%')
        .padding(16)
        .borderRadius(16)
        .backgroundColor(this.isDarkMode ? '#252525' : '#F0F5FF')
        .margin({ top: 8, left: 16, right: 16 })
        .onClick(() => {
          // 点击资产信息卡片时隐藏搜索界面
          this.hideSearchInterface();
        })

        // 物品分类标签页
        Row() {
          // 全部标签
          Column() {
            Text('全部')
              .fontSize(16)
              .fontWeight(this.currentTab === 0 ? FontWeight.Bold : FontWeight.Normal)
              .fontColor(this.currentTab === 0 ? '#FFFFFF' : this.colors.PRIMARY_COLOR)
            
            Text(`${this.getTotalCount()}`)
              .fontSize(14)
              .fontColor(this.currentTab === 0 ? '#FFFFFF' : this.colors.PRIMARY_COLOR)
              .margin({ top: 4 })
          }
          .layoutWeight(1)
          .backgroundColor(this.currentTab === 0 ? this.colors.PRIMARY_COLOR : this.colors.CARD_BACKGROUND)
          .borderRadius(8)
          .padding({ top: 8, bottom: 8 })
          .onClick(() => this.currentTab = 0)
          
          // 已过期标签
          Column() {
            Text('已过期')
              .fontSize(16)
              .fontWeight(this.currentTab === 1 ? FontWeight.Bold : FontWeight.Normal)
              .fontColor(this.currentTab === 1 ? '#FFFFFF' : this.colors.ERROR_COLOR)
            
            Text(`${this.getExpiredCount()}`)
              .fontSize(14)
              .fontColor(this.currentTab === 1 ? '#FFFFFF' : this.colors.ERROR_COLOR)
              .margin({ top: 4 })
          }
          .layoutWeight(1)
          .backgroundColor(this.currentTab === 1 ? this.colors.ERROR_COLOR : this.colors.CARD_BACKGROUND)
          .borderRadius(8)
          .padding({ top: 8, bottom: 8 })
          .onClick(() => this.currentTab = 1)
          
          // 临期标签
          Column() {
            Text('临期')
              .fontSize(16)
              .fontWeight(this.currentTab === 2 ? FontWeight.Bold : FontWeight.Normal)
              .fontColor(this.currentTab === 2 ? '#FFFFFF' : this.colors.WARNING_COLOR)
            
            Text(`${this.getExpiringCount()}`)
              .fontSize(14)
              .fontColor(this.currentTab === 2 ? '#FFFFFF' : this.colors.WARNING_COLOR)
              .margin({ top: 4 })
          }
          .layoutWeight(1)
          .backgroundColor(this.currentTab === 2 ? this.colors.WARNING_COLOR : this.colors.CARD_BACKGROUND)
          .borderRadius(8)
          .padding({ top: 8, bottom: 8 })
          .onClick(() => this.currentTab = 2)
          
          // 正常标签
          Column() {
            Text('正常')
              .fontSize(16)
              .fontWeight(this.currentTab === 3 ? FontWeight.Bold : FontWeight.Normal)
              .fontColor(this.currentTab === 3 ? '#FFFFFF' : this.colors.SUCCESS_COLOR)
            
            Text(`${this.getLongTermCount()}`)
              .fontSize(14)
              .fontColor(this.currentTab === 3 ? '#FFFFFF' : this.colors.SUCCESS_COLOR)
              .margin({ top: 4 })
          }
          .layoutWeight(1)
          .backgroundColor(this.currentTab === 3 ? this.colors.SUCCESS_COLOR : this.colors.CARD_BACKGROUND)
          .borderRadius(8)
          .padding({ top: 8, bottom: 8 })
          .onClick(() => this.currentTab = 3)
        }
        .width('100%')
        .padding({ left: 16, right: 16, top: 16, bottom: 8 })
        .onClick(() => {
          // 点击分类标签行时隐藏搜索界面
          this.hideSearchInterface();
        })

        // 物品列表
        List() {
          ForEach(this.getFilteredMedicineList(), (item: MedicineItem, index: number) => {
            ListItem() {
              Row() {
                Row() {
                  // 物品类型图标
                  Image(getItemTypeIcon(item.itemType))
                    .width(40)
                    .height(40)
                    .margin({ right: 12 })

                }
                .margin({ right: 16 })

                Column() {
                  Row() {
                    Text(item.name)
                      .fontSize(18)
                      .fontWeight(FontWeight.Medium)
                      .fontColor(this.colors.PRIMARY_TEXT)
                    
                    // 添加物品类型标签
                    Text(getItemTypeName(item.itemType))
                      .fontSize(12)
                      .fontColor(this.colors.SECONDARY_TEXT)
                      .backgroundColor(this.isDarkMode ? '#333333' : '#F0F0F0')
                      .borderRadius(10)
                      .padding({ left: 8, right: 8, top: 2, bottom: 2 })
                      .margin({ left: 8 })
                  }
                  .margin({ bottom: 8 })

                  Text(item.location)
                    .fontSize(14)
                    .fontColor(this.colors.SECONDARY_TEXT)
                    .opacity(0.6)
                }
                .alignItems(HorizontalAlign.Start)
                .layoutWeight(1)

                // 过期标签
                Text(this.getExpiryText(item.expireDay))
                  .fontSize(10)
                  .fontWeight(FontWeight.Medium)
                  .fontColor('#FFFFFF') // 使用白色文本确保在彩色背景上有良好的可读性
                  .backgroundColor(this.getExpiryBgColor(item.expireDay))
                  .borderRadius(12)
                  .padding({ left: 10, right: 10, top: 5, bottom: 5 })
                  .margin({ left: 3, right: 16 })
              }
              .width('100%')
              .padding(16)
              .backgroundColor(this.colors.CARD_BACKGROUND)
              .borderRadius(8)
              .margin({ bottom: 8, left: 16, right: 16 })
              .onClick(() => {
                // 点击物品项打开详情弹窗
                this.selectedItem = item;
                this.openDetailDialog();
              })
            }
          })
        }
        .width('100%')
        .height('60%')
        .margin({ top: 16 })
        .backgroundColor(this.colors.PAGE_BACKGROUND)
        .padding({ top: 8, bottom: 8 })
        .listDirection(Axis.Vertical)
        .edgeEffect(EdgeEffect.Spring)
        .scrollBar(BarState.Auto)
        .friction(0.6)
        .layoutWeight(1)
        .onClick(() => {
          // 点击物品列表区域时隐藏搜索界面
          this.hideSearchInterface();
        })

        // 遮罩层 - 只覆盖物品详情页面上方的区域
        if (this.showDetailDialog) {
          Rect()
            .width('100%')
            .height('60%') // 只覆盖上方区域，约占屏幕60%的高度
            .position({ x: 0, y: 0 }) // 从屏幕顶部开始
            .fill('rgba(0, 0, 0, 0.3)')
            .opacity(this.maskOpacity)
            .onClick(() => {
              this.closeDetailDialog();
            })
            .animation({
              duration: 250,
              curve: Curve.EaseOut,
              iterations: 1,
              playMode: PlayMode.Normal
            })
        }
        
        // 语音弹窗遮罩层
        if (this.showVoiceDialog) {
          Rect()
            .width('100%')
            .height('100%') // 覆盖整个屏幕
            .position({ x: 0, y: 0 }) // 从屏幕顶部开始
            .fill('rgba(0, 0, 0, 0.3)')
            .opacity(this.voiceDialogOpacity)
            .onClick(() => {
              this.closeVoiceDialog();
            })
            .animation({
              duration: 300,
              curve: Curve.EaseOut,
              iterations: 1,
              playMode: PlayMode.Normal
            })
        }
        
        // 侧边菜单遮罩层
        if (this.showSideMenu) {
          Rect()
            .width('100%') // 修改宽度为100%，刚好覆盖屏幕宽度
            .height('100%') // 修改高度为100%，刚好覆盖屏幕高度
            .position({ x: '0%', y: '0%' }) // 调整位置为正常位置，不需要偏移
            .fill('#000000')
            .opacity(this.sideMenuMaskOpacity)
            .zIndex(2000)
            .onClick(() => {
              this.closeSideMenu();
            })
            .animation({
              duration: 300,
              curve: Curve.EaseOut,
              iterations: 1,
              playMode: PlayMode.Normal
            })
        }

        // 侧边菜单
        if (this.showSideMenu) {
          Column() {
            // 顶部区域
            Column() {
              Row() {
                // 根据编辑状态显示文本或输入框
                if (this.editUserName) {
                  TextInput({ text: this.tempUserName })
                    .width('70%')
                    .height(40)
                    .fontSize(20)
                    .fontWeight(FontWeight.Medium)
                    .backgroundColor(this.isDarkMode ? '#252525' : '#F5F5F5')
                    .borderRadius(8)
                    .padding({ left: 10, right: 10 })
                    .fontColor(this.colors.PRIMARY_TEXT)
                    .placeholderColor(this.colors.HINT_TEXT)
                    .maxLength(10) // 限制最大输入长度为10个字符
                    .onChange((value) => {
                      this.tempUserName = value;
                      // 如果超过10个字符，显示提示信息
                      if (value.length > 10) {
                        promptAction.showToast({ message: '用户名不能超过10个字符' });
                      }
                    })
                    .onSubmit(() => {
                      this.saveUserName();
                    })
                    .onBlur(() => {
                      this.saveUserName();
                    })
                } else {
                  Text(this.userName + '的Home')
                    .fontSize(20)
                    .fontWeight(FontWeight.Medium)
                    .fontColor(this.colors.PRIMARY_TEXT)
                    .onClick(() => {
                      this.editUserName = true;
                      this.tempUserName = this.userName;
                    })
                }
                
                // 会员版标签
                Text('会员版')
                  .fontSize(12)
                  .fontColor('#FFFFFF')
                  .backgroundColor('#FFA940')
                  .borderRadius(10)
                  .padding({ left: 8, right: 8, top: 2, bottom: 2 })
                  .margin({ left: 8 })
              }
              .width('100%')
              .justifyContent(FlexAlign.Start)
              .padding({ top: 40, bottom: 20, left: 20, right: 20 })
            }
            .width('100%')
            .backgroundColor(this.colors.CARD_BACKGROUND)
            .borderRadius({ topRight: 16 }) // 添加顶部右侧圆角
            
            // 中间空白区域
            Blank()
              .layoutWeight(1)
              .backgroundColor(this.colors.CARD_BACKGROUND)
              .onClick(() => {
                if (this.editUserName) {
                  this.saveUserName();
                }
              })
            
            // 底部按钮 - 设置放在最下面
            Column() {
              // 消息通知按钮
              Row() {
                Stack() {
                  Image($r('app.media.notification_bell'))
                    .width(28)
                    .height(28)
                    .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
                  
                  // 未读消息徽章
                  if (this.unreadNotifications > 0) {
                    Circle({ width: 16, height: 16 })
                      .fill('#E53935')
                      .position({ x: 16, y: -4 })
                    
                    Text(this.unreadNotifications > 9 ? '9+' : this.unreadNotifications.toString())
                      .fontSize(10)
                      .fontColor(Color.White)
                      .fontWeight(FontWeight.Bold)
                      .position({ x: 16, y: -4 })
                      .textAlign(TextAlign.Center)
                      .width(16)
                      .height(16)
                  }
                }
                .width(28)
                .height(28)
                
                Text('消息通知')
                  .fontSize(14)
                  .fontColor(this.colors.PRIMARY_TEXT)
                  .margin({ left: 12 })
              }
              .width('100%')
              .padding({ left: 20, right: 20, top: 12, bottom: 12 })
              .onClick(() => {
                // 先提示点击了消息通知按钮（用于调试）
                promptAction.showToast({ message: '正在跳转到消息通知页面...' });
                
                // 关闭侧边菜单（先关闭菜单再进行导航）
                this.closeSideMenu();
                
                // 延迟执行导航操作，确保侧边栏已关闭
                setTimeout(() => {
                  try {
                    // 使用简单路径格式导航
                    router.pushUrl({
                      url: 'pages/Notifications'
                    })
                    .then(() => {
                      // 导航成功
                      console.info('成功导航到消息通知页面');
                    })
                    .catch((err: RouterError) => {
                      // 导航失败
                      console.error('导航到消息通知页面失败:', err);
                      promptAction.showToast({ 
                        message: '导航失败: ' + (err.message || '')
                      });
                    });
                  } catch (error) {
                    console.error('导航操作异常:', error);
                    promptAction.showToast({ message: '导航出现异常' });
                  }
                }, 350); // 等待侧边栏关闭动画完成
              })
              
              // 帮助中心按钮
              Row() {
                Image($r('app.media.help_hand_drawn'))
                  .width(28)
                  .height(28)
                  .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
                Text('帮助中心')
                  .fontSize(14)
                  .fontColor(this.colors.PRIMARY_TEXT)
                  .margin({ left: 12 })
              }
              .width('100%')
              .padding({ left: 20, right: 20, top: 12, bottom: 12 })
              .onClick(() => {
                promptAction.showToast({ message: '点击了帮助中心' });
              })
              
              // 登录/登出按钮
              Row() {
                Image($r('app.media.user'))
                  .width(28)
                  .height(28)
                  .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
                Text(this.isLoggedIn ? '退出登录' : '登录账号')
                  .fontSize(14)
                  .fontColor(this.colors.PRIMARY_TEXT)
                  .margin({ left: 12 })
              }
              .width('100%')
              .padding({ left: 20, right: 20, top: 12, bottom: 12 })
              .onClick(() => {
                // 关闭侧边菜单（先关闭菜单再进行导航）
                this.closeSideMenu();
                
                // 延迟执行导航操作，确保侧边栏已关闭
                setTimeout(() => {
                  if (this.isLoggedIn) {
                    // 已登录状态，执行登出操作
                    promptAction.showToast({ message: '正在退出登录...' });
                    // 设置用户名为默认值
                    this.userManager.setUsername('未登录');
                    // 更新登录状态
                    this.checkLoginStatus();
                  } else {
                    // 未登录状态，跳转到登录页面
                    promptAction.showToast({ message: '正在跳转到登录页面...' });
                    router.pushUrl({ url: 'pages/Login' })
                      .then(() => {
                        // 导航成功
                      })
                      .catch((err: RouterError) => {
                        // 导航失败
                        promptAction.showToast({ 
                          message: '导航失败'
                        });
                      });
                  }
                }, 350); // 等待侧边栏关闭动画完成
              })
              
              // 设置按钮 - 移到最底部
              Row() {
                Image($r('app.media.settings_hand_drawn'))
                  .width(28)
                  .height(28)
                  .fillColor(this.isDarkMode ? '#AAAAAA' : '#666666')
                Text('设置')
                  .fontSize(14)
                  .fontColor(this.colors.PRIMARY_TEXT)
                  .margin({ left: 12 })
              }
              .width('100%')
              .padding({ left: 20, right: 20, top: 12, bottom: 12 })
              .onClick(() => {
                // 先提示点击了设置按钮（用于调试）
                promptAction.showToast({ message: '正在跳转到设置页面...' });
                
                // 关闭侧边菜单（先关闭菜单再进行导航）
                this.closeSideMenu();
                
                // 延迟执行导航操作，确保侧边栏已关闭
                setTimeout(() => {
                  // 使用与Index.ets页面相同的简单导航方式
                  promptAction.showToast({ message: '正在导航...' });
                  
                  // 使用最简单的导航方式
                  router.pushUrl({ url: 'pages/Settings' })
                    .then(() => {
                      // 导航成功
                    })
                    .catch((err: RouterError) => {
                      // 导航失败 - 使用自定义的 RouterError 类型
                      promptAction.showToast({ 
                        message: '导航失败'
                      });
                    });
                }, 350); // 等待侧边栏关闭动画完成
              })
            }
            .width('100%')
            .margin({ bottom: 20 })
            .backgroundColor(this.colors.CARD_BACKGROUND)
            .borderRadius({ bottomRight: 16 }) // 添加底部右侧圆角
          }
          .width('66%')
          .height('100%')
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .borderRadius({ topRight: 16, bottomRight: 16 })
          .position({ x: this.sideMenuTranslateX, y: 0 })
          .zIndex(2001)
          .animation({
            duration: 300,
            curve: Curve.EaseOut,
            iterations: 1,
            playMode: PlayMode.Normal
          })
        }
        
        // 物品详情弹窗
        if (this.showDetailDialog) {
          Column() {
            // 顶部菜单栏
            Row() {
              Text(this.isEditMode ? '保存' : '编辑')
                .fontSize(16)
                .fontColor('#999999')
                .onClick(() => {
                  if (this.isEditMode) {
                    // 保存编辑内容
                    this.saveItemChanges();
                  } else {
                    // 进入编辑模式
                    this.enterEditMode();
                  }
                })
              
              Text(this.isAddingNewItem ? '添加物品' : (this.isEditMode ? '编辑物品' : '物品详情'))
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
              
              Text('关闭')
                .fontSize(16)
                .fontColor('#999999')
                .onClick(() => {
                  if (this.isEditMode) {
                    // 取消编辑并回到详情模式
                    this.cancelEdit();
                  } else {
                    // 直接关闭对话框
                    this.closeDetailDialog();
                  }
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .borderRadius({ topLeft: 16, topRight: 16 })
            .backgroundColor('rgba(255, 255, 255, 0.95)')
            
            // 物品信息部分 - 使用Scroll让内容可滚动
            Scroll() {
              Column() {
                Row() {
                  // 物品图标
                  Image(getItemTypeIcon(this.selectedItem.itemType))
                    .width(50)
                    .height(50)
                    .margin({ right: 16 })
                  
                  Column() {
                    if (this.isEditMode) {
                      if (this.isAddingNewItem) {
                        // 添加新物品时显示名称输入框
                        TextInput({ 
                          placeholder: '请输入物品名称',
                          text: this.editName 
                        })
                          .fontSize(18)
                          .fontWeight(FontWeight.Bold)
                          .width('100%')
                          .margin({ bottom: 6 })
                          .onChange((value) => this.editName = value)
                      } else {
                        // 编辑现有物品时显示名称文本
                        Text(this.selectedItem.name)
                          .fontSize(18)
                          .fontWeight(FontWeight.Bold)
                          .margin({ bottom: 6 })
                      }
                      
                      Row() {
                        Text(this.formatLocation(this.selectedLocation))
                          .fontSize(14)
                          .fontColor('#666666')
                          .width('80%')
                          .textAlign(TextAlign.Start)
                        
                        Image($r('app.media.search_icon'))
                          .width(16)
                          .height(16)
                          .margin({ left: 5 })
                          .objectFit(ImageFit.Contain)
                          .fillColor('#666666')
                      }
                      .width('100%')
                      .onClick(() => {
                        this.openLocationPicker();
                      })
                    } else {
                      Text(this.selectedItem.name || '新物品')
                        .fontSize(18)
                        .fontWeight(FontWeight.Bold)
                        .margin({ bottom: 6 })
                      
                      Text(this.formatLocation(this.selectedLocation))
                        .fontSize(14)
                        .fontColor('#666666')
                    }
                  }
                  .alignItems(HorizontalAlign.Start)
                  .layoutWeight(1)
                  
                  // 过期标签
                  Text(this.getExpiryText(this.dynamicExpiryDays))
                    .fontSize(12)
                    .fontWeight(FontWeight.Medium)
                    .fontColor('#FFFFFF') // 使用白色文本确保在彩色背景上有良好的可读性
                    .backgroundColor(this.getExpiryBgColor(this.dynamicExpiryDays))
                    .borderRadius(16)
                    .padding({ left: 10, right: 10, top: 4, bottom: 4 })
                }
                .width('100%')
                .padding({ left: 16, right: 16, top: 16, bottom: 16 })
                .backgroundColor('#F5F7FA')
                .margin({ top: 1 })
                
                // 物品详细属性表
                Column() {
                  // 物品类别
                  Row() {
                    Text('物品类别：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    if (this.isEditMode) {
                      Row() {
                        Text(getItemTypeName(this.selectedItemType))
                          .fontSize(14)
                          .fontColor('#333333')
                        
                        Image($r('app.media.search_icon'))
                          .width(16)
                          .height(16)
                          .margin({ left: 5 })
                          .fillColor('#666666')
                      }
                      .width('50%')
                      .justifyContent(FlexAlign.End)
                      .onClick(() => {
                        this.openTypeSelector();
                      })
                    } else {
                      Text(getItemTypeName(this.selectedItem.itemType))
                        .fontSize(14)
                        .fontColor('#333333')
                        .width('50%')
                        .textAlign(TextAlign.End)
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 物品金额
                  Row() {
                    Text('物品金额：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    if (this.isEditMode) {
                      TextInput({ text: this.editPrice })
                        .fontSize(14)
                        .fontColor('#333333')
                        .width('50%')
                        .textAlign(TextAlign.End)
                        .onChange((value) => this.editPrice = value)
                    } else {
                      Text('¥ ' + this.editPrice)
                        .fontSize(14)
                        .fontColor('#333333')
                        .width('50%')
                        .textAlign(TextAlign.End)
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 日均价格
                  Row() {
                    Text('日均价格：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    Text(this.calculateDailyAverage(this.selectedItem))
                      .fontSize(14)
                      .fontColor('#333333')
                      .width('50%')
                      .textAlign(TextAlign.End)
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 购买日期
                  Row() {
                    Text('购买日期：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    if (this.isEditMode) {
                      Row() {
                        // 显示当前选择的购买日期，点击后打开日期选择对话框
                        Text(this.editPurchaseDate || '请选择日期')
                          .fontSize(14)
                          .fontColor('#333333')
                          .width('80%')
                          .textAlign(TextAlign.End)
                          .onClick(() => {
                            // 解析当前日期字符串为Date对象
                            let initialDate = new Date();
                            if (this.editPurchaseDate) {
                              const dateParts = this.editPurchaseDate.split('/').map(Number);
                              if (dateParts.length === 3) {
                                initialDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
                              }
                            }
                            
                            // 确定最小可选日期：如果生产日期已设置，则使用生产日期；否则使用2020-01-01
                            let minDate = new Date('2020-01-01');
                            if (this.editProductionDate) {
                              const productionParts = this.editProductionDate.split('/').map(Number);
                              if (productionParts.length === 3) {
                                minDate = new Date(productionParts[0], productionParts[1] - 1, productionParts[2]);
                              }
                            }
                            
                            // 打开日期选择对话框
                            DatePickerDialog.show({
                              start: minDate,
                              end: new Date(),
                              selected: initialDate,
                              onAccept: (value) => {
                                // 使用新的工具函数格式化日期，修复月份处理错误
                                const formattedDate = this.formatDatePickerValue(value as DatePickerValue);
                                if (formattedDate) {
                                  // 更新购买日期
                                  this.editPurchaseDate = formattedDate;
                                  console.info('购买日期更新为:', formattedDate);
                                  
                                  // 验证购买日期与生产日期的关系
                                  this.validatePurchaseAndProductionDates();
                                }
                              }
                            })
                          })
                      }
                      .width('50%')
                      .justifyContent(FlexAlign.End)
                    } else {
                      Text(this.editPurchaseDate || '未设置')
                        .fontSize(14)
                        .fontColor('#333333')
                        .width('50%')
                        .textAlign(TextAlign.End)
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 生产日期
                  Row() {
                    Text('生产日期：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    if (this.isEditMode) {
                      Row() {
                        // 显示当前选择的日期，点击后打开日期选择对话框
                        Text(this.editProductionDate || '请选择日期')
                          .fontSize(14)
                          .fontColor('#333333')
                          .width('80%')
                          .textAlign(TextAlign.End)
                          .onClick(() => {
                            // 解析当前日期字符串为Date对象
                            let initialDate = new Date();
                            if (this.editProductionDate) {
                              const dateParts = this.editProductionDate.split('/').map(Number);
                              if (dateParts.length === 3) {
                                initialDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
                              }
                            }
                            
                            // 打开日期选择对话框
                            DatePickerDialog.show({
                              start: new Date('2020-01-01'),
                              end: new Date(),
                              selected: initialDate,
                              onAccept: (value) => {
                                // 使用新的工具函数格式化日期，修复月份处理错误
                                const formattedDate = this.formatDatePickerValue(value as DatePickerValue);
                                if (formattedDate) {
                                  // 更新生产日期
                                  this.editProductionDate = formattedDate;
                                
                                  // 如果保质期限不为空，计算保质期至日期
                                  const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
                                    ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
                                    : !this.editExpiryDays || this.editExpiryDays.trim() === '';
                                  
                                  if (!isExpiryPeriodEmpty) {
                                    // 更新保质期至日期
                                    this.calculateExpiryDate();
                                  }
                                  
                                  // 验证购买日期与生产日期的关系
                                  this.validatePurchaseAndProductionDates();
                                }
                              }
                            })
                          })
                      }
                      .width('50%')
                      .justifyContent(FlexAlign.End)
                    } else {
                      Text(this.editProductionDate || '未设置')
                        .fontSize(14)
                        .fontColor('#333333')
                        .width('50%')
                        .textAlign(TextAlign.End)
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 保质期限
                  Column() {
                    Row() {
                      Text('保质期限：')
                        .fontSize(14)
                        .fontColor('#666666')
                        .width('50%')
                        .textAlign(TextAlign.Start)
                      
                      if (this.isEditMode) {
                        Row() {
                          // 保质期输入框
                          TextInput({ 
                            text: this.expiryUnitIsMonth ? this.editExpiryMonths : this.editExpiryDays 
                          })
                            .fontSize(14)
                            .fontColor('#333333')
                            .width('60%')
                            .textAlign(TextAlign.End)
                            .onChange((value) => {
                              // 允许保质期限为空值
                              if (this.expiryUnitIsMonth) {
                                this.editExpiryMonths = value;
                                // 如果输入为空，设置为无保质期
                                if (!value || value.trim() === '') {
                                  this.expiryDate = '无保质期';
                                  this.dynamicExpiryDays = 30; // 设置为长期有效
                                  console.info('保质期限(月)设置为空，物品为无保质期');
                                } else {
                                  // 同时计算保质期至日期
                                  this.calculateExpiryDate();
                                }
                                console.info('保质期限(月)修改为:', value, '保质期至:', this.expiryDate);
                              } else {
                                this.editExpiryDays = value;
                                // 如果输入为空，设置为无保质期
                                if (!value || value.trim() === '') {
                                  this.expiryDate = '无保质期';
                                  this.dynamicExpiryDays = 30; // 设置为长期有效
                                  console.info('保质期限(天)设置为空，物品为无保质期');
                                } else {
                                  // 同时计算保质期至日期
                                  this.calculateExpiryDate();
                                }
                                console.info('保质期限(天)修改为:', value, '保质期至:', this.expiryDate);
                              }
                            })
                          
                          // 单位切换按钮
                          Text(this.expiryUnitIsMonth ? '月' : '天')
                            .fontSize(14)
                            .fontColor('#3478F6')
                            .margin({ left: 4 })
                            .onClick(() => {
                              this.expiryUnitIsMonth = !this.expiryUnitIsMonth;
                              
                              // 如果两个字段都有值，进行换算
                              if (this.editExpiryMonths && this.editExpiryMonths.trim() !== '' &&
                                  this.editExpiryDays && this.editExpiryDays.trim() !== '') {
                                // 如果从月切换到天，自动换算（假设1个月=30天）
                                if (!this.expiryUnitIsMonth) {
                                  this.editExpiryDays = String(Math.round(Number(this.editExpiryMonths) * 30));
                                } 
                                // 如果从天切换到月，自动换算
                                else {
                                  this.editExpiryMonths = String(Math.round(Number(this.editExpiryDays) / 30));
                                }
                              }
                              // 如果只有一个字段有值，计算另一个
                              else if (this.editExpiryMonths && this.editExpiryMonths.trim() !== '') {
                                // 月份有值，计算天数
                                this.editExpiryDays = String(Math.round(Number(this.editExpiryMonths) * 30));
                              }
                              else if (this.editExpiryDays && this.editExpiryDays.trim() !== '') {
                                // 天数有值，计算月份
                                this.editExpiryMonths = String(Math.round(Number(this.editExpiryDays) / 30));
                              }
                              
                              // 更新保质期至日期
                              this.calculateExpiryDate();
                              console.info('单位切换为:', this.expiryUnitIsMonth ? '月' : '天', 
                                          '保质期限:', this.expiryUnitIsMonth ? this.editExpiryMonths : this.editExpiryDays,
                                          '保质期至:', this.expiryDate);
                            })
                        }
                        .width('50%')
                        .justifyContent(FlexAlign.End)
                      } else {
                        // 显示模式下只显示一种单位的保质期，或者为空
                        Text(this.getExpiryPeriodDisplayText())
                          .fontSize(14)
                          .fontColor('#333333')
                          .width('50%')
                          .textAlign(TextAlign.End)
                      }
                    }
                    
                    // 点击提示文本（仅在编辑模式下显示）
                    if (this.isEditMode) {
                      Text('点击"' + (this.expiryUnitIsMonth ? '月' : '天') + '"可切换单位')
                        .fontSize(12)
                        .fontColor('#999999')
                        .width('100%')
                        .textAlign(TextAlign.End)
                        .margin({ top: 4, right: 16 })
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                  
                  // 保质期至
                  Row() {
                    Text('保质期至：')
                      .fontSize(14)
                      .fontColor('#666666')
                      .width('50%')
                      .textAlign(TextAlign.Start)
                    
                    if (this.isEditMode) {
                      Row() {
                        // 显示当前选择的日期，点击后打开日期选择对话框
                        Text(this.expiryDate && this.expiryDate.trim() !== '' && this.expiryDate !== '无保质期' ? this.expiryDate : '无保质期')
                          .fontSize(14)
                          .fontColor(this.expiryDate === '无保质期' ? '#52C41A' : '#333333')
                          .width('80%')
                          .textAlign(TextAlign.End)
                          .onClick(() => {
                            // 检查保质期限是否为空，如果为空，则不允许选择保质期至日期
                            const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
                              ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
                              : !this.editExpiryDays || this.editExpiryDays.trim() === '';
                            
                            if (isExpiryPeriodEmpty) {
                              promptAction.showToast({ message: '请先设置保质期限' });
                              return;
                            }

                            // 解析当前日期字符串为Date对象
                            let initialDate = new Date();
                            initialDate.setFullYear(initialDate.getFullYear() + 1); // 默认为一年后
                            
                            if (this.expiryDate && this.expiryDate !== '无保质期' && this.expiryDate.trim() !== '') {
                              const dateParts = this.expiryDate.split('/').map(Number);
                              if (dateParts.length === 3) {
                                initialDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
                              }
                            }
                            
                            // 打开日期选择对话框
                            DatePickerDialog.show({
                              start: new Date('2020-01-01'),
                              end: new Date('2100-12-31'), // 允许设置未来日期
                              selected: initialDate,
                              onAccept: (value) => {
                                // 使用工具函数格式化日期
                                const formattedDate = this.formatDatePickerValue(value as DatePickerValue);
                                
                                if (formattedDate) {
                                  // 更新保质期至日期
                                  this.expiryDate = formattedDate;
                                  
                                  // 根据保质期至日期反向计算保质期限
                                  this.calculateExpiryPeriod();
                                  
                                  // 日志记录
                                  console.info('保质期至修改为:', formattedDate, 
                                               '保质期限月:', this.editExpiryMonths, 
                                               '保质期限天:', this.editExpiryDays);
                                }
                              }
                            })
                          })
                      }
                      .width('50%')
                      .justifyContent(FlexAlign.End)
                    } else {
                      Text(this.expiryDate && this.expiryDate.trim() !== '' && this.expiryDate !== '无保质期' ? this.expiryDate : '无保质期')
                        .fontSize(14)
                        .fontColor(this.expiryDate === '无保质期' ? '#52C41A' : this.getExpiryColor(this.dynamicExpiryDays))
                        .width('50%')
                        .textAlign(TextAlign.End)
                    }
                  }
                  .width('100%')
                  .padding({ top: 14, bottom: 14 })
                  .border({ width: { bottom: 0.5 }, color: '#F0F0F0' })
                }
                .width('100%')
                .padding({ left: 16, right: 16 })
                .backgroundColor(Color.White)
                
                // 删除按钮
                Button('删除')
                  .width('92%')
                  .height(44)
                  .fontSize(16)
                  .fontColor('#E53935')
                  .backgroundColor('#FFE0E0')
                  .margin({ top: 20, bottom: 16 })
                  .onClick(() => {
                    // 保存当前要删除的物品信息
                    const itemToDelete = this.selectedItem;
                    
                    // 使用简单提示询问用户是否确定删除（不要关闭详情对话框）
                    promptAction.showDialog({
                      title: '确认删除',
                      message: `确定要删除"${itemToDelete.name}"吗？`,
                      buttons: [
                        {
                          text: '取消',
                          color: this.isDarkMode ? '#AAAAAA' : '#666666'
                        },
                        {
                          text: '删除',
                          color: '#E53935'
                        }
                      ]
                    })
                    .then(result => {
                      // 如果用户点击了"删除"按钮(index为1)
                      if (result.index === 1) {
                        // 删除物品
                        this.deleteItem(itemToDelete);
                        
                        // 关闭物品详情对话框
                        this.closeDetailDialog();
                      } else {
                        // 用户取消了删除，不需要任何操作
                        console.info('用户取消了删除操作');
                      }
                    })
                    .catch((err: Error) => {
                      console.error('删除对话框显示失败', err);
                    });
                  })
              }
              .width('100%')
            }
            .scrollable(ScrollDirection.Vertical)  // 允许垂直滚动
            .scrollBar(BarState.Auto)  // 自动显示滚动条
            .edgeEffect(EdgeEffect.Spring)  // 边缘效果
            .width('100%')
            .layoutWeight(1)  // 占用剩余空间
          }
          .width('100%')
          .height(this.dialogHeight)
          .backgroundColor('rgba(255, 255, 255, 0.95)')
          .borderRadius({ topLeft: 16, topRight: 16 })
          .position({ x: 0, y: '100%' })
          .translate({ y: -this.dialogHeight + this.dialogTranslateY })
          .animation({
            duration: 250,
            curve: Curve.EaseOut,
            iterations: 1,
            playMode: PlayMode.Normal
          })
        }
        
        // 语音输入弹窗
        if (this.showVoiceDialog) {
          Column() {
            // 顶部菜单栏
            Row() {
              Text('取消')
                .fontSize(16)
                .fontColor('#999999')
                .onClick(() => {
                  this.closeVoiceDialog();
                })
              
              Text('语音输入')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
              
              Text('确认')
                .fontSize(16)
                .fontColor('#3478F6')
                .onClick(() => {
                  this.confirmVoiceInput();
                })
            }
            .width('100%')
            .justifyContent(FlexAlign.SpaceBetween)
            .padding({ left: 20, right: 20, top: 12, bottom: 12 })
            .borderRadius({ topLeft: 16, topRight: 16 })
            .backgroundColor('#FFFFFF')
            
            // 语音输入内容区域
            Column() {
              // 富文本框区域 - 显示语音案例和识别结果
              TextArea({
                placeholder: this.voiceExampleText,
                text: this.isRecording && this.realtimeText ? this.realtimeText : this.voiceInputText
              })
                .width('90%')
                .height(80)
                .backgroundColor('#F8F9FA')
                .borderRadius(12)
                .padding(16)
                .fontSize(14)
                .fontColor((this.voiceInputText || this.realtimeText) ? '#333333' : '#999999')
                .placeholderColor('#999999')
                .margin({ bottom: 8 })
                .inputFilter('')

              // 识别状态提示和错误信息
              if (this.recognitionStatus || this.recognitionError) {
                Text(this.recognitionError || this.recognitionStatus)
                  .fontSize(12)
                  .fontColor(this.recognitionError ? '#FF4444' : (this.isRecording ? '#3478F6' : '#999999'))
                  .margin({ bottom: 12 })
                  .textAlign(TextAlign.Center)
                  .width('90%')
                  .maxLines(2)
                  .textOverflow({ overflow: TextOverflow.Ellipsis })
              }
              
              // 录音按钮区域
              Column() {
                // 双按钮水平排列
                Row() {
                  // 录音按钮
                  Button({ type: ButtonType.Normal }) {
                    Column() {
                      if (this.isRecording) {
                        // 录音中显示倒计时和动画效果
                        Stack() {
                          Circle()
                            .width(32)
                            .height(32)
                            .fill('#FF4444')
                            .opacity(0.3)
                            .animation({
                              duration: 1000,
                              curve: Curve.EaseInOut,
                              iterations: -1,
                              playMode: PlayMode.Alternate
                            })

                          Text(`${60 - this.recordingTime}`)
                            .fontSize(16)
                            .fontWeight(FontWeight.Bold)
                            .fontColor('#FF4444')
                        }
                        .margin({ bottom: 4 })

                        Text('点击停止')
                          .fontSize(10)
                          .fontColor('#FF4444')
                          .fontWeight(FontWeight.Medium)
                      } else if (this.isRecognizing) {
                        // 识别中显示加载动画
                        LoadingProgress()
                          .width(24)
                          .height(24)
                          .color(this.colors.PRIMARY_COLOR)
                          .margin({ bottom: 4 })

                        Text('识别中')
                          .fontSize(10)
                          .fontColor(this.colors.PRIMARY_COLOR)
                          .fontWeight(FontWeight.Medium)
                      } else {
                        // 默认状态
                        Image($r('app.media.mic_icon'))
                          .width(24)
                          .height(24)
                          .fillColor(this.colors.PRIMARY_COLOR)
                          .margin({ bottom: 4 })
                        Text('录音')
                          .fontSize(12)
                          .fontColor(this.colors.PRIMARY_COLOR)
                          .fontWeight(FontWeight.Medium)
                      }
                    }
                  }
                  .width(80)
                  .height(70)
                  .backgroundColor(this.isRecording ? '#FFE6E6' : (this.isRecognizing ? '#FFF3E0' : '#F0F7FF'))
                  .borderRadius(12)
                  .shadow({ radius: 8, color: this.isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)', offsetX: 0, offsetY: 2 })
                  .enabled(true) // 始终启用按钮，允许用户停止录音
                  .onClick(() => {
                    this.handleVoiceInput();
                  })
                  
                  // 按钮间距
                  Blank().width(16)
                  
                  // 转换按钮
                  Button({ type: ButtonType.Normal }) {
                    Column() {
                      Image($r('app.media.edit'))
                        .width(24)
                        .height(24)
                        .fillColor('#FFFFFF')
                        .margin({ bottom: 4 })
                      Text('数据转化')
                        .fontSize(12)
                        .fontColor('#FFFFFF')
                        .fontWeight(FontWeight.Medium)
                    }
                  }
                  .width(80)
                  .height(70)
                  .backgroundColor(this.colors.PRIMARY_COLOR)
                  .borderRadius(12)
                  .shadow({ radius: 8, color: this.isDarkMode ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.1)', offsetX: 0, offsetY: 2 })
                  .onClick(() => {
                    this.handleConvertButton();
                  })
                }
                .width('100%')
                .justifyContent(FlexAlign.Center)
                .margin({ bottom: 16 })
                

              }
              .width('100%')
              .padding({ top: 12, bottom: 20 })
              .alignItems(HorizontalAlign.Center)
              

            }
            .width('100%')
            .layoutWeight(1)
          }
          .width('100%')
          .height(480)
          .backgroundColor('#FFFFFF')
          .borderRadius({ topLeft: 16, topRight: 16 })
          .position({ x: 0, y: '100%' })
          .translate({ y: -480 + this.voiceDialogTranslateY })
          .opacity(this.voiceDialogOpacity)
          .zIndex(2100)
          .animation({
            duration: 300,
            curve: Curve.EaseOut,
            iterations: 1,
            playMode: PlayMode.Normal
          })
        }
        
        // 注意：已移除旧的日期选择器弹窗，改为直接在字段内使用日期选择器组件

        // 位置选择器背景遮罩
        if (this.showLocationPicker) {
          Rect()
            .width('100%')
            .height('100%')
            .position({ x: 0, y: 0 })
            .fill('rgba(0, 0, 0, 0.4)')
            .zIndex(2000)
            .onClick(() => {
              this.closeLocationPicker();
            })
            .animation({
              duration: 250,
              curve: Curve.EaseOut
            })
        }

        // 位置选择器弹窗
        if (this.showLocationPicker) {
          Column() {
            // 顶部拖动条
            Divider()
              .width(36)
              .height(4)
              .borderRadius(2)
              .color('#DDDDDD')
              .margin({ top: 8, bottom: 8 })
            
            // 选择器顶部操作栏
            Row() {
              Button('取消')
                .fontColor('#999999')
                .backgroundColor(Color.Transparent)
                .onClick(() => {
                  this.closeLocationPicker();
                })
              
              Text('选择位置')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
              
              Button('确定')
                .fontColor('#3478F6')
                .backgroundColor(Color.Transparent)
                .onClick(() => {
                  // 保存选择的位置
                  this.selectedLocation = [...this.tempLocation];
                  this.editLocation = this.formatLocation(this.selectedLocation);
                  this.showLocationPicker = false;
                  this.showCustomInput = false;
                })
            }
            .width('100%')
            .padding({ left: 16, right: 16, top: 12, bottom: 12 })
            .justifyContent(FlexAlign.SpaceBetween)
            .backgroundColor(Color.White)
            
            // 位置级别选择器
            Row() {
              ForEach(this.locationLevels.map((_, i) => i), (levelIndex: number) => {
                Column() {
                  Text(['区域', '家具', '具体位置'][levelIndex])
                    .fontSize(14)
                    .fontColor('#999999')
                    .padding({ bottom: 8 })
                  
                  Text(this.tempLocation[levelIndex] || '请选择')
                    .fontSize(16)
                    .fontColor(this.currentPickerLevel === levelIndex ? '#3478F6' : '#333333')
                    .fontWeight(this.currentPickerLevel === levelIndex ? FontWeight.Medium : FontWeight.Normal)
                }
                .width('33%')
                .alignItems(HorizontalAlign.Center)
                .onClick(() => {
                  // 切换当前编辑的层级
                  this.currentPickerLevel = levelIndex;
                  this.showCustomInput = false;
                  
                  // 如果选择了第一级，确保第二级选项已更新
                  if (levelIndex === 0 && this.tempLocation[0]) {
                    this.updateSecondLevelOptions(this.tempLocation[0]);
                  }
                })
              })
            }
            .width('100%')
            .padding({ left: 16, right: 16, top: 16, bottom: 12 })
            .backgroundColor('#F5F5F5')
            
            // 自定义输入框
            if (this.showCustomInput) {
              Column() {
                TextInput({ placeholder: '请输入自定义位置', text: this.customCategory })
                  .width('80%')
                  .height(40)
                  .fontSize(16)
                  .margin({ top: 16, bottom: 16 })
                  .backgroundColor('#F0F0F0')
                  .padding(8)
                  .borderRadius(8)
                  .onChange((value: string) => {
                    this.customCategory = value;
                  })
                
                Button('添加')
                  .width('80%')
                  .height(40)
                  .backgroundColor('#3478F6')
                  .borderRadius(20)
                  .onClick(() => {
                    if (this.customCategory.trim() !== '') {
                      // 添加自定义类别
                      this.locationLevels[this.currentPickerLevel].splice(
                        this.locationLevels[this.currentPickerLevel].length - 1, 
                        0, 
                        this.customCategory.trim()
                      );
                      
                      // 选中新添加的类别
                      this.tempLocation[this.currentPickerLevel] = this.customCategory.trim();
                      
                      // 重置状态
                      this.customCategory = '';
                      this.showCustomInput = false;
                    }
                  })
              }
              .width('100%')
              .backgroundColor(Color.White)
              .alignItems(HorizontalAlign.Center)
            } else {
              // 位置选择器
              List() {
                ForEach(this.locationLevels[this.currentPickerLevel], (location: string, index: number) => {
                  ListItem() {
                    Row() {
                      Text(location)
                        .fontSize(16)
                        .fontColor(this.tempLocation[this.currentPickerLevel] === location ? '#3478F6' : '#333333')
                        .fontWeight(this.tempLocation[this.currentPickerLevel] === location ? FontWeight.Medium : FontWeight.Normal)
                      
                      if (location === '其他') {
                        Image($r('app.media.add'))
                          .width(16)
                          .height(16)
                          .margin({ left: 8 })
                          .fillColor('#3478F6')
                      }
                    }
                    .width('100%')
                    .justifyContent(FlexAlign.Center)
                    .padding({ top: 16, bottom: 16 })
                  }
                  .backgroundColor(this.tempLocation[this.currentPickerLevel] === location ? '#F0F7FF' : Color.White)
                  .onClick(() => {
                    if (location === '其他') {
                      // 显示自定义输入框
                      this.showCustomInput = true;
                    } else {
                      // 选择该位置
                      this.tempLocation[this.currentPickerLevel] = location;
                      
                      // 如果选择的是第一级，更新第二级选项
                      if (this.currentPickerLevel === 0) {
                        this.updateSecondLevelOptions(location);
                      }
                      
                      // 自动前进到下一级
                      if (this.currentPickerLevel < 2) {
                        this.currentPickerLevel++;
                      }
                    }
                  })
                  .gesture(
                    LongPressGesture({ repeat: false })
                      .onAction(() => {
                        // 忽略"其他"选项的长按操作
                        if (location !== '其他') {
                          // 保存当前编辑的词条信息
                          this.editingLocationLevel = this.currentPickerLevel;
                          this.editingLocationItem = location;
                          this.editingLocationNewValue = location;
                          // 显示编辑对话框
                          this.showLocationEditDialog = true;
                        }
                      })
                  )
                })
              }
              .width('100%')
              .height(280)
              .backgroundColor(Color.White)
              .divider({ strokeWidth: 1, color: '#F0F0F0', startMargin: 16, endMargin: 16 })
            }
          }
          .width('100%')
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 16, topRight: 16 })
          .position({ x: 0, y: '100%' })
          .translate({ y: this.showLocationPicker ? '-100%' : '0' })
          .zIndex(2100)
          .shadow({ radius: 12, color: 'rgba(0, 0, 0, 0.15)', offsetX: 0, offsetY: -4 })
          .animation({
            duration: 250,
            curve: Curve.EaseOut
          })
        }

        // 类型选择器背景遮罩
        if (this.showTypeSelector) {
          Rect()
            .width('100%')
            .height('100%')
            .position({ x: 0, y: 0 })
            .fill('rgba(0, 0, 0, 0.4)')
            .zIndex(2000)
            .onClick(() => {
              this.showTypeSelector = false;
            })
            .animation({
              duration: 250,
              curve: Curve.EaseOut
            })
        }
        
        // 类型选择器弹窗
        if (this.showTypeSelector) {
          Column() {
            // 选择器顶部操作栏
            Row() {
              Button('取消')
                .fontColor('#999999')
                .backgroundColor(Color.Transparent)
                .onClick(() => {
                  this.showTypeSelector = false;
                })
              
              Text('选择物品类型')
                .fontSize(16)
                .fontWeight(FontWeight.Medium)
              
              Button('确定')
                .fontColor('#3478F6')
                .backgroundColor(Color.Transparent)
                .onClick(() => {
                  if (this.isAddingNewItem) {
                    // 更新新物品的类型
                    this.selectedItem = new MedicineItem(
                      this.editName,
                      this.formatLocation(this.selectedLocation),
                      $r('app.media.add'),
                      0,
                      this.selectedItemType,
                      20.00,
                      "month"
                    );
                  } else {
                    // 更新现有物品的类型
                    this.selectedItem.itemType = this.selectedItemType;
                  }
                  this.showTypeSelector = false;
                })
            }
            .width('100%')
            .padding({ left: 16, right: 16, top: 12, bottom: 12 })
            .justifyContent(FlexAlign.SpaceBetween)
            .backgroundColor(Color.White)
            
            // 类型选择列表
            List() {
              // 输入框用于添加新类型
              if (this.showAddTypeInput) {
                ListItem() {
                  Column() {
                    TextInput({ 
                      placeholder: '请输入新类型名称',
                      text: this.newTypeName
                    })
                      .width('80%')
                      .height(40)
                      .fontSize(16)
                      .margin({ top: 16, bottom: 16 })
                      .backgroundColor('#F0F0F0')
                      .padding(8)
                      .borderRadius(8)
                      .onChange((value: string) => {
                        this.newTypeName = value;
                      })
                    
                    Row() {
                      Button('取消', { type: ButtonType.Normal })
                        .width(100)
                        .height(40)
                        .backgroundColor('#F0F0F0')
                        .borderRadius(20)
                        .fontColor('#666666')
                        .onClick(() => {
                          this.showAddTypeInput = false;
                          this.newTypeName = '';
                        })
                      
                      Button('添加', { type: ButtonType.Normal })
                        .width(100)
                        .height(40)
                        .backgroundColor('#3478F6')
                        .borderRadius(20)
                        .fontColor(Color.White)
                        .margin({ left: 16 })
                        .onClick(() => {
                          if (this.newTypeName.trim() !== '') {
                            // 添加新类型
                            addCustomType(this.newTypeName.trim());
                            // 更新类型列表
                            this.itemTypes = getAllItemTypes();
                            // 重置输入框
                            this.newTypeName = '';
                            this.showAddTypeInput = false;
                            // 提示添加成功
                            promptAction.showToast({ message: '添加类型成功' });
                          } else {
                            promptAction.showToast({ message: '类型名称不能为空' });
                          }
                        })
                    }
                    .margin({ bottom: 16 })
                  }
                  .width('100%')
                  .backgroundColor('#FFFFFF')
                  .alignItems(HorizontalAlign.Center)
                }
              }
              
              // 现有类型列表
              ForEach(this.itemTypes, (itemType: ItemType) => {
                ListItem() {
                  Row() {
                    Image(getItemTypeIcon(itemType))
                      .width(24)
                      .height(24)
                      .margin({ right: 12 })
                    
                    Text(getItemTypeName(itemType))
                      .fontSize(16)
                      .fontColor(this.selectedItemType === itemType ? '#3478F6' : '#333333')
                      .fontWeight(this.selectedItemType === itemType ? FontWeight.Medium : FontWeight.Normal)
                  }
                  .width('100%')
                  .justifyContent(FlexAlign.Start)
                  .padding({ left: 16, right: 16, top: 16, bottom: 16 })
                }
                .backgroundColor(this.selectedItemType === itemType ? '#F0F7FF' : Color.White)
                .onClick(() => {
                  this.selectedItemType = itemType;
                })
              })
              
              // 添加类型按钮（放在最下面）
              ListItem() {
                Row() {
                  Image($r('app.media.add'))
                    .width(24)
                    .height(24)
                    .margin({ right: 12 })
                    .fillColor('#3478F6')
                  
                  Text('添加新类型')
                    .fontSize(16)
                    .fontColor('#3478F6')
                    .fontWeight(FontWeight.Medium)
                }
                .width('100%')
                .justifyContent(FlexAlign.Start)
                .padding({ left: 16, right: 16, top: 16, bottom: 16 })
              }
              .backgroundColor(Color.White)
              .onClick(() => {
                this.showAddTypeInput = true;
              })
            }
            .width('100%')
            .height(280)
            .backgroundColor(Color.White)
            .divider({ strokeWidth: 1, color: '#F0F0F0' })
          }
          .width('100%')
          .backgroundColor(Color.White)
          .borderRadius({ topLeft: 16, topRight: 16 })
          .position({ x: 0, y: '100%' })
          .translate({ y: this.showTypeSelector ? '-100%' : '0' })
          .zIndex(2100)
          .shadow({ radius: 12, color: 'rgba(0, 0, 0, 0.15)', offsetX: 0, offsetY: -4 })
          .animation({
            duration: 250,
            curve: Curve.EaseOut
          })
        }
      }
      .onClick(() => {
        if (this.showSearchBubbles) {
          this.showSearchBubbles = false;
        }
      })
      .onTouch(() => {
        if (this.showSearchBubbles) {
          this.showSearchBubbles = false;
        }
      })
      .width('100%')
      .height('100%')
      .backgroundColor(this.colors.PAGE_BACKGROUND)
      
      // 点击空白区域关闭下拉菜单的遮罩层
      if (this.showAddMenu) {
        Rect()
          .width('100%') 
          .height('100%')
          .position({ x: 0, y: 0 })
          .fill('transparent')
          .zIndex(1999)
          .onClick(() => {
            this.closeAddMenu();
          })
      }
      
      // 添加菜单下拉框
      if (this.showAddMenu) {
        Column() {
          // 文字添加选项
          Row() {
            Image($r('app.media.search_icon'))
              .width(22)
              .height(22)
              .margin({ right: 10 })
              .fillColor('#4CAF50')
            Text('文字添加')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .height(48)
          .padding({ left: 14, right: 14 })
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .borderRadius({ topLeft: 16, topRight: 16 })
          .onClick(() => {
            this.createNewItem();
          })
          
          Divider().height(1).color(this.colors.DIVIDER_COLOR)
          
          // 语音添加选项
          Row() {
            Image($r('app.media.mic_icon'))
              .width(22)
              .height(22)
              .margin({ right: 10 })
              .fillColor('#FFA940')
            Text('语音添加')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .height(48)
          .padding({ left: 14, right: 14 })
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .onClick(() => {
            this.openVoiceDialog();
            this.closeAddMenu();
          })
          
          Divider().height(1).color(this.colors.DIVIDER_COLOR)
          
          // 拍照添加选项
          Row() {
            Image($r('app.media.add'))
              .width(22)
              .height(22)
              .margin({ right: 10 })
              .fillColor('#3478F6')
            Text('拍照添加')
              .fontSize(16)
              .fontColor(this.colors.PRIMARY_TEXT)
              .fontWeight(FontWeight.Medium)
          }
          .width('100%')
          .height(48)
          .padding({ left: 14, right: 14 })
          .backgroundColor(this.colors.CARD_BACKGROUND)
          .borderRadius({ bottomLeft: 16, bottomRight: 16 })
          .onClick(() => {
            promptAction.showToast({ message: '点击了拍照添加' });
            this.closeAddMenu();
          })
        }
        .width('35%')
        .borderRadius(16)
        .backgroundColor(this.colors.CARD_BACKGROUND)
        .position({ right: '16px', top: 120 })
        .translate({ y: this.menuTranslateY })
        .zIndex(2000)
        .opacity(this.menuOpacity)
        .shadow({ radius: 12, color: 'rgba(0, 0, 0, 0.15)', offsetX: 0, offsetY: 6 })
        .animation({
          duration: 250,
          curve: Curve.EaseOut,
          iterations: 1,
          playMode: PlayMode.Normal
        })
      }
      
      // 调试按钮 - 仅在开发环境使用
      Button("数据库测试")
        .position({ x: '70%', y: '92%' })
        .backgroundColor('#666666')
        .fontSize(12)
        .height(36)
        .onClick(() => {
          router.pushUrl({
            url: 'pages/DatabaseTest'
          }).catch((err: RouterError) => {
            console.error('导航到数据库测试页面失败', err);
          });
        })
    }
  }

  /**
   * 打开详情对话框
   */
  openDetailDialog() {
    // 初始化编辑字段
    this.initEditFields();
    
    // 不需要重复计算保质期至日期，initEditFields已处理
    // 也不需要设置动态过期天数，避免覆盖已计算的值
    
    // 初始时设置偏移量为底部（即完全隐藏）
    this.dialogTranslateY = this.dialogHeight;
    
    // 先显示对话框（但在底部不可见）
    this.showDetailDialog = true;
    
    // 在下一帧动画滑出
    setTimeout(() => {
      this.maskOpacity = 0.5;
      this.dialogTranslateY = 0; // 滑动到完全可见位置
    }, 10);
  }

  /**
   * 关闭详情对话框
   */
  closeDetailDialog() {
    this.isEditMode = false;
    this.isAddingNewItem = false;
    this.showLocationPicker = false;
    this.showTypeSelector = false;
    this.showCustomInput = false;
    this.showLocationEditDialog = false;
    this.showAddTypeInput = false;
    this.newTypeName = '';
    
    // 滑回底部
    this.dialogTranslateY = this.dialogHeight;
    this.maskOpacity = 0;
    
    // 为了确保动画完成后再隐藏对话框
    setTimeout(() => {
      this.showDetailDialog = false;
    }, 250);
  }
  
  /**
   * 初始化编辑字段
   */
  initEditFields() {
    // 添加详细调试信息
    console.info('=== 开始初始化编辑字段 ===');
    console.info('selectedItem数据:', JSON.stringify({
      id: this.selectedItem.id,
      name: this.selectedItem.name,
      productionDate: this.selectedItem.productionDate,
      purchaseDate: this.selectedItem.purchaseDate,
      createTime: this.selectedItem.createTime,
      updateTime: this.selectedItem.updateTime,
      expireDay: this.selectedItem.expireDay,
      expiryUnit: this.selectedItem.expiryUnit
    }, null, 2));
    
    if (this.selectedItem.productionDate) {
      console.info('生产日期时间戳:', this.selectedItem.productionDate);
      console.info('生产日期转换结果:', new Date(this.selectedItem.productionDate));
    } else {
      console.info('警告：selectedItem中没有生产日期数据！');
    }
    
    // 设置基本字段
    this.editName = this.selectedItem.name;
    this.editPrice = this.selectedItem.price.toString();
    
    // 初始化基本信息
    this.editLocation = this.selectedItem.location;
    
    // 初始化物品类型
    this.selectedItemType = this.selectedItem.itemType;
    
    // 初始化位置数组
    if (this.isAddingNewItem) {
      this.selectedLocation = ['客厅', '茶几', '上层'];
    } else {
      const locationParts = this.selectedItem.location.split('/');
      this.selectedLocation = [
        locationParts[0] || '客厅',
        locationParts[1] || '茶几',
        locationParts[2] || '上层'
      ];
    }
    this.tempLocation = [...this.selectedLocation];
    
    // 确保第二级选项与第一级选择匹配
    this.updateSecondLevelOptions(this.selectedLocation[0]);
    
    // 初始化其他字段
    this.editExpiryMonths = this.selectedItem.expiryUnit === "month" ? '12' : '365';
    this.editExpiryDays = this.selectedItem.expiryUnit === "month" ? '365' : '365';
    this.expiryUnitIsMonth = this.selectedItem.expiryUnit === "month";
    
    // 初始化购买日期
    if (this.isAddingNewItem) {
      // 对于新物品，默认为当前日期
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      this.editPurchaseDate = `${year}/${month}/${day}`;
    } else {
      // 对于现有物品，从数据库读取购买日期
      if (this.selectedItem.purchaseDate) {
        const purchaseDate = new Date(this.selectedItem.purchaseDate);
        const year = purchaseDate.getFullYear();
        const month = String(purchaseDate.getMonth() + 1).padStart(2, '0');
        const day = String(purchaseDate.getDate()).padStart(2, '0');
        this.editPurchaseDate = `${year}/${month}/${day}`;
      } else {
        // 如果没有购买日期数据，使用创建时间或当前日期
        const fallbackDate = this.selectedItem.createTime ? new Date(this.selectedItem.createTime) : new Date();
        const year = fallbackDate.getFullYear();
        const month = String(fallbackDate.getMonth() + 1).padStart(2, '0');
        const day = String(fallbackDate.getDate()).padStart(2, '0');
        this.editPurchaseDate = `${year}/${month}/${day}`;
      }
    }
    
    // 初始化保质期相关字段
    if (this.isAddingNewItem) {
      // 对于新物品，使用默认值
      this.editExpiryMonths = '12';
      this.editExpiryDays = '365';
      this.expiryUnitIsMonth = true; // 默认使用月作为单位
      
      // 初始化日期为当天
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      this.editProductionDate = `${year}/${month}/${day}`;
      
      // 设置初始默认值的保质期至日期（一年后）
      const tempExpiryDate = new Date(today);
      tempExpiryDate.setFullYear(tempExpiryDate.getFullYear() + 1);
      const expYear = tempExpiryDate.getFullYear();
      const expMonth = String(tempExpiryDate.getMonth() + 1).padStart(2, '0');
      const expDay = String(tempExpiryDate.getDate()).padStart(2, '0');
      this.expiryDate = `${expYear}/${expMonth}/${expDay}`;
      
      // 计算保质期至日期和动态过期天数
      this.calculateExpiryDate();
    } else {
      // 对于现有物品，根据实际剩余天数计算
      const remainingDays = this.selectedItem.expireDay;
      
      // 设置保质期单位
      this.expiryUnitIsMonth = this.selectedItem.expiryUnit === "month";
      
      // 判断是否为无保质期/长期有效（30天）
      if (remainingDays === 30) {
        // 无保质期的情况
        this.editExpiryMonths = '';
        this.editExpiryDays = '';
        this.expiryDate = '无保质期';
        this.dynamicExpiryDays = 30;
        console.info('初始化编辑字段 - 物品ID:', this.selectedItem.id, '无保质期');
      } else {
        // 有保质期的情况
        if (this.expiryUnitIsMonth) {
          // 如果单位是月，计算月数
          this.editExpiryMonths = String(Math.round(remainingDays / 30));
          // 同时更新天数显示
          this.editExpiryDays = String(remainingDays);
        } else {
          // 如果单位是天，直接使用天数
          this.editExpiryDays = String(remainingDays);
          // 同时更新月数显示
          this.editExpiryMonths = String(Math.round(remainingDays / 30));
        }
        
        console.info('初始化编辑字段 - 物品ID:', this.selectedItem.id, 
                     '剩余天数:', remainingDays,
                     '月数:', this.editExpiryMonths,
                     '天数:', this.editExpiryDays,
                     '单位:', this.selectedItem.expiryUnit);
                     
        // 对于现有物品，从数据库恢复生产日期（这是关键修复！）
        if (this.selectedItem.productionDate) {
          const productionDate = new Date(this.selectedItem.productionDate);
          const year = productionDate.getFullYear();
          const month = String(productionDate.getMonth() + 1).padStart(2, '0');
          const day = String(productionDate.getDate()).padStart(2, '0');
          this.editProductionDate = `${year}/${month}/${day}`;
          console.info('恢复现有物品生产日期:', this.editProductionDate, '时间戳:', this.selectedItem.productionDate);
        } else {
          // 如果没有生产日期数据，使用创建时间或当前日期作为fallback
          const fallbackDate = this.selectedItem.createTime ? new Date(this.selectedItem.createTime) : new Date();
          const year = fallbackDate.getFullYear();
          const month = String(fallbackDate.getMonth() + 1).padStart(2, '0');
          const day = String(fallbackDate.getDate()).padStart(2, '0');
          this.editProductionDate = `${year}/${month}/${day}`;
          console.info('使用fallback生产日期:', this.editProductionDate);
        }
        
        // 计算保质期至日期（基于生产日期，而不是当前日期）
        if (remainingDays > 0) {
          // 基于生产日期和保质期长度计算保质期至日期
          const productionDate = this.selectedItem.productionDate ? 
            new Date(this.selectedItem.productionDate) : 
            (this.selectedItem.createTime ? new Date(this.selectedItem.createTime) : new Date());
          
          const expiryDate = new Date(productionDate);
          expiryDate.setDate(expiryDate.getDate() + remainingDays);
          
          // 格式化保质期至日期
          const expYear = expiryDate.getFullYear();
          const expMonth = String(expiryDate.getMonth() + 1).padStart(2, '0');
          const expDay = String(expiryDate.getDate()).padStart(2, '0');
          this.expiryDate = `${expYear}/${expMonth}/${expDay}`;
          
          console.info('初始化编辑字段 - 保质期至日期:', this.expiryDate);
        } else {
          // 已经过期的情况，基于生产日期计算过期日期
          const productionDate = this.selectedItem.productionDate ? 
            new Date(this.selectedItem.productionDate) : 
            (this.selectedItem.createTime ? new Date(this.selectedItem.createTime) : new Date());
          
          const expiryDate = new Date(productionDate);
          expiryDate.setDate(expiryDate.getDate() + Math.abs(remainingDays)); // 使用绝对值计算原保质期
          
          // 格式化保质期至日期
          const expYear = expiryDate.getFullYear();
          const expMonth = String(expiryDate.getMonth() + 1).padStart(2, '0');
          const expDay = String(expiryDate.getDate()).padStart(2, '0');
          this.expiryDate = `${expYear}/${expMonth}/${expDay}`;
          
          console.info('初始化编辑字段 - 已过期，保质期至日期:', this.expiryDate);
        }
      }
    }
  }
  
  /**
   * 进入编辑模式
   */
  enterEditMode() {
    this.isEditMode = true;
    // 初始化临时位置
    this.tempLocation = [...this.selectedLocation];
  }
  
  /**
   * 取消编辑
   */
  cancelEdit() {
    this.isEditMode = false;
    this.initEditFields();
    this.showLocationPicker = false;
    this.showCustomInput = false;
  }
  
  /**
   * 更新第二级位置选项
   * @param firstLevel 第一级选择的位置
   */
  updateSecondLevelOptions(firstLevel: string) {
    // 根据第一级选择更新第二级选项
    if (this.locationMap[firstLevel]) {
      this.locationLevels[1] = this.locationMap[firstLevel];
      
      // 如果当前选择的第二级不在新的选项列表中，重置为第一个选项
      if (this.tempLocation[1] && !this.locationMap[firstLevel].includes(this.tempLocation[1])) {
        this.tempLocation[1] = this.locationMap[firstLevel][0];
      }
    } else {
      // 如果没有对应的第二级选项，设置为空数组
      this.locationLevels[1] = ['其他'];
      this.tempLocation[1] = '其他';
    }
  }
  
  /**
   * 关闭位置选择器
   */
  closeLocationPicker() {
    this.showLocationPicker = false;
    this.showCustomInput = false;
    // 恢复临时位置为当前选择的位置
    this.tempLocation = [...this.selectedLocation];
  }
  
  /**
   * 保存物品修改
   */
  saveItemChanges() {
    if (this.isAddingNewItem) {
      // 添加新物品
      if (this.editName.trim() === '') {
        promptAction.showToast({ message: '请输入物品名称' });
        return;
      }
      
      // 保质期限和保质期至日期可以为空，无需验证
      // 但需确保它们的状态保持一致
      const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
        ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
        : !this.editExpiryDays || this.editExpiryDays.trim() === '';
      
      const isExpiryDateEmpty = !this.expiryDate || this.expiryDate.trim() === '' || this.expiryDate === '无保质期';
      
      // 确保一致性：处理无保质期的情况
      if (isExpiryPeriodEmpty) {
        // 如果保质期限为空，则设置为无保质期
        this.expiryDate = '无保质期';
        this.dynamicExpiryDays = 30; // 设置为长期有效
      } else if (isExpiryDateEmpty) {
        // 如果保质期限有值但保质期至为空或为"无保质期"，则计算保质期至
        this.calculateExpiryDate();
      } else {
        // 确保根据最新的保质期限重新计算dynamicExpiryDays
        this.calculateExpiryDate();
      }
      
      // 日志记录当前值
      console.info('添加新物品 - 保质期限:', this.expiryUnitIsMonth ? this.editExpiryMonths : this.editExpiryDays, 
                   '保质期至:', this.expiryDate, 
                   '动态过期天数:', this.dynamicExpiryDays);
      
      // 创建新物品对象
      const newItem = new MedicineItem(
        this.editName,
        this.formatLocation(this.selectedLocation),
        $r('app.media.add'),
        this.dynamicExpiryDays,
        this.selectedItemType,
        parseFloat(this.editPrice) || 0,
        this.expiryUnitIsMonth ? "month" : "day"
      );
      
      // 设置购买日期
      if (this.editPurchaseDate) {
        const dateParts = this.editPurchaseDate.split('/').map(Number);
        if (dateParts.length === 3) {
          const purchaseDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
          newItem.purchaseDate = purchaseDate.getTime();
        }
      }
      
      // 设置生产日期
      if (this.editProductionDate) {
        const dateParts = this.editProductionDate.split('/').map(Number);
        if (dateParts.length === 3) {
          const productionDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
          newItem.productionDate = productionDate.getTime();
          console.info('设置新物品生产日期:', this.editProductionDate, '时间戳:', newItem.productionDate);
        }
      }
      
      // 日志记录将要保存的值
      console.info('保存到数据库 - 新物品过期天数:', newItem.expireDay, 
                   '保质期单位:', newItem.expiryUnit,
                   '购买日期:', this.editPurchaseDate,
                   '生产日期:', this.editProductionDate);
      
      // 添加到数据库
      this.dbManager.addItem(newItem, (id: number) => {
        if (id > 0) {
          // 添加成功，重新加载物品列表
          this.loadItemsFromDB();
          promptAction.showToast({ message: '添加成功' });
        } else {
          promptAction.showToast({ message: '添加失败' });
        }
      });
    } else {
      // 更新现有物品
      const index = this.medicineList.findIndex(item => 
        item.name === this.selectedItem.name && 
        item.location === this.selectedItem.location);
      
      if (index !== -1) {
        // 保质期限和保质期至日期可以为空，无需验证
        // 但需确保它们的状态保持一致
        const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
          ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
          : !this.editExpiryDays || this.editExpiryDays.trim() === '';
        
        const isExpiryDateEmpty = !this.expiryDate || this.expiryDate.trim() === '' || this.expiryDate === '无保质期';
        
        // 确保一致性：处理无保质期的情况
        if (isExpiryPeriodEmpty) {
          // 如果保质期限为空，则设置为无保质期
          this.expiryDate = '无保质期';
          this.dynamicExpiryDays = 30; // 设置为长期有效
        } else if (isExpiryDateEmpty) {
          // 如果保质期限有值但保质期至为空或为"无保质期"，则计算保质期至
          this.calculateExpiryDate();
        } else {
          // 确保根据最新的保质期限重新计算dynamicExpiryDays
          this.calculateExpiryDate();
        }
        
        // 日志记录当前值
        console.info('保存前状态 - 保质期限:', this.expiryUnitIsMonth ? this.editExpiryMonths : this.editExpiryDays, 
                     '保质期至:', this.expiryDate, 
                     '动态过期天数:', this.dynamicExpiryDays);
        
        // 更新物品信息
        this.selectedItem.location = this.formatLocation(this.selectedLocation);
        this.selectedItem.itemType = this.selectedItemType;
        this.selectedItem.expireDay = this.dynamicExpiryDays;
        this.selectedItem.price = parseFloat(this.editPrice) || 0;
        this.selectedItem.expiryUnit = this.expiryUnitIsMonth ? "month" : "day";
        
        // 更新购买日期
        if (this.editPurchaseDate) {
          const dateParts = this.editPurchaseDate.split('/').map(Number);
          if (dateParts.length === 3) {
            const purchaseDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
            this.selectedItem.purchaseDate = purchaseDate.getTime();
          }
        }
        
        // 更新生产日期
        if (this.editProductionDate) {
          const dateParts = this.editProductionDate.split('/').map(Number);
          if (dateParts.length === 3) {
            const productionDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
            this.selectedItem.productionDate = productionDate.getTime();
            console.info('更新物品生产日期:', this.editProductionDate, '时间戳:', this.selectedItem.productionDate);
          }
        }
        
        // 日志记录更新后的值
        console.info('保存到数据库 - ID:', this.selectedItem.id, 
                     '过期天数:', this.selectedItem.expireDay,
                     '保质期单位:', this.selectedItem.expiryUnit,
                     '购买日期:', this.editPurchaseDate,
                     '生产日期:', this.editProductionDate);
        
        // 更新数据库
        if (this.selectedItem.id !== undefined) {
          this.dbManager.updateItem(this.selectedItem.id, this.selectedItem, (success: boolean) => {
            if (success) {
              // 更新成功，重新加载物品列表
              this.loadItemsFromDB();
              promptAction.showToast({ message: '保存成功' });
            } else {
              promptAction.showToast({ message: '保存失败' });
            }
          });
        } else {
          promptAction.showToast({ message: '保存失败：物品ID不存在' });
        }
      }
    }
    
    // 退出编辑模式并关闭对话框
    this.isEditMode = false;
    this.isAddingNewItem = false;
    this.showLocationPicker = false;
    this.showCustomInput = false;
    this.showTypeSelector = false;
    this.closeDetailDialog();
  }

  /**
   * 获取过期文本
   */
  getExpiryText(days: number): string {
    if (days <= 0) {
      return '已过期';
    } else if (days <= 7) {
      return '7天内过期';
    } else {
      return '长期有效';
    }
  }

  /**
   * 获取过期标签文字颜色
   */
  getExpiryColor(days: number): string {
    if (days <= 0) {
      return this.isDarkMode ? '#FF7875' : '#FF4D4F'; // 已过期 - 红色
    } else if (days <= 7) {
      return this.isDarkMode ? '#FFC069' : '#FA8C16'; // 临期 - 橙色
    } else {
      return this.isDarkMode ? '#95DE64' : '#52C41A'; // 正常 - 绿色
    }
  }

  /**
   * 获取过期标签背景颜色
   */
  getExpiryBgColor(days: number): string {
    if (days <= 0) {
      return '#FF4D4F'; // 已过期 - 红色
    } else if (days <= 7) {
      return '#FA8C16'; // 临期 - 橙色
    } else {
      return '#52C41A'; // 正常 - 绿色
    }
  }
  
  /**
   * 获取保质期限显示文本
   */
  getExpiryPeriodDisplayText(): string {
    // 检查是否处于编辑模式
    if (this.isEditMode) {
      // 在编辑模式下根据当前选择的单位显示
      // 检查保质期限是否为空
      const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
        ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
        : !this.editExpiryDays || this.editExpiryDays.trim() === '';
      
      // 如果保质期限为空，则显示无保质期
      if (isExpiryPeriodEmpty) {
        return '无保质期';
      }
      
      // 根据当前选择的单位显示对应的保质期限
      if (this.expiryUnitIsMonth && this.editExpiryMonths && this.editExpiryMonths.trim() !== '') {
        return this.editExpiryMonths + '个月';
      } else if (!this.expiryUnitIsMonth && this.editExpiryDays && this.editExpiryDays.trim() !== '') {
        return this.editExpiryDays + '天';
      }
    } else {
      // 在显示模式下根据物品的保存单位显示
      if (this.selectedItem.expireDay === 30) {
        return '无保质期';
      }
      
      if (this.selectedItem.expiryUnit === "month") {
        // 显示月份
        const months = Math.round(this.selectedItem.expireDay / 30);
        return months + '个月';
      } else {
        // 显示天数
        return this.selectedItem.expireDay + '天';
      }
    }
    
    // 默认返回无保质期
    return '无保质期';
  }
  
  /**
   * 计算物品日均价格
   * @param item 物品对象
   * @returns 日均价格显示文本
   */
  calculateDailyAverage(item: MedicineItem): string {
    try {
      // 获取当前价格（编辑模式下使用editPrice，否则使用item.price）
      let currentPrice: number;
      if (this.isEditMode) {
        currentPrice = parseFloat(this.editPrice) || 0;
      } else {
        currentPrice = item.price || 0;
      }
      
      // 检查价格是否有效
      if (currentPrice <= 0) {
        return '价格未设置';
      }
      
      // 获取购买日期（编辑模式下使用editPurchaseDate，否则使用item.purchaseDate）
      let purchaseDateTimestamp: number;
      if (this.isEditMode && this.editPurchaseDate) {
        const dateParts = this.editPurchaseDate.split('/').map(Number);
        if (dateParts.length === 3) {
          const purchaseDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
          purchaseDateTimestamp = purchaseDate.getTime();
        } else {
          return '购买日期格式错误';
        }
      } else if (item.purchaseDate) {
        purchaseDateTimestamp = item.purchaseDate;
      } else {
        return '购买日期未设置';
      }
      
      const today = new Date();
      const purchaseDate = new Date(purchaseDateTimestamp);
      
      // 计算结束日期
      let endDate: Date;
      let isExpired = false;
      
      // 判断是否有保质期
      if (item.expireDay === 30) {
        // 无保质期情况：使用当前日期作为结束日期
        endDate = today;
      } else {
        // 有保质期情况：使用保质期至日期作为结束日期
        // 根据购买日期和剩余天数计算保质期至日期
        const currentRemainingDays = item.expireDay;
        endDate = new Date(today);
        endDate.setDate(endDate.getDate() + currentRemainingDays);
        
        // 如果已经过期，仍然使用保质期至日期计算
        if (currentRemainingDays <= 0) {
          isExpired = true;
        }
      }
      
      // 计算天数差
      const timeDiff = endDate.getTime() - purchaseDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
      
      // 确保天数至少为1天，避免除零错误
      const actualDays = Math.max(daysDiff, 1);
      
      // 计算日均价格
      const dailyAverage = currentPrice / actualDays;
      
      // 格式化显示
      const formattedAmount = dailyAverage.toFixed(2);
      
      // 根据不同情况返回适当的显示文本
      if (item.expireDay === 30) {
        return `¥${formattedAmount}/天（至今）`;
      } else if (isExpired) {
        return `¥${formattedAmount}/天（已过期）`;
      } else {
        return `¥${formattedAmount}/天`;
      }
      
    } catch (error) {
      console.error('计算日均价格出错', error);
      return '计算出错';
    }
  }
  
  /**
   * 获取物品总数量
   */
  getTotalCount(): number {
    return this.medicineList.length;
  }

  /**
   * 更新总件数
   */
  updateTotalCount() {
    this.totalCount = this.medicineList.length;
  }
  
  /**
   * 获取已过期物品数量
   */
  getExpiredCount(): number {
    return this.medicineList.filter(item => item.expireDay <= 0).length;
  }
  
  /**
   * 获取即将过期物品数量（7日内到期）
   */
  getExpiringCount(): number {
    return this.medicineList.filter(item => item.expireDay > 0 && item.expireDay <= 7).length;
  }
  
  /**
   * 获取长期有效物品数量
   */
  getLongTermCount(): number {
    return this.medicineList.filter(item => item.expireDay > 7).length;
  }
  
  /**
   * 搜索物品
   */
  searchItems() {
    if (this.searchText.trim() === '') {
      // 关键词为空，加载所有物品
      this.loadItemsFromDB();
      this.isSearching = false;
    } else {
      // 使用关键词搜索物品
      this.dbManager.searchItems(this.searchText, (items: MedicineItem[]) => {
        this.medicineList = items;
        this.totalCount = items.length;
        this.calculateTotalAssets();
        this.isSearching = true;
      });
    }
  }

  /**
   * 根据当前选中的标签页过滤物品列表
   */
  getFilteredMedicineList(): Array<MedicineItem> {
    let filteredList: Array<MedicineItem> = [];
    
    // 先根据标签页过滤
    switch(this.currentTab) {
      case 1: // 已过期
        filteredList = this.medicineList.filter(item => item.expireDay <= 0);
        // 按名称排序
        filteredList.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 2: // 临期
        filteredList = this.medicineList.filter(item => item.expireDay > 0 && item.expireDay <= 7);
        // 按名称排序
        filteredList.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 3: // 正常
        filteredList = this.medicineList.filter(item => item.expireDay > 7);
        // 按名称排序
        filteredList.sort((a, b) => a.name.localeCompare(b.name));
        break;
      default: // 全部
        // 对于全部标签，先复制整个列表，然后根据过期程度进行排序
        filteredList = [...this.medicineList];
        
        // 排序：今日到期(0) > 7日内到期(3) > 长期有效(7)
        filteredList.sort((a, b) => {
          // 首先按照过期天数升序排序（0在前，7在后）
          if (a.expireDay !== b.expireDay) {
            return a.expireDay - b.expireDay;
          }
          
          // 如果过期天数相同，则按名称字母顺序排序
          return a.name.localeCompare(b.name);
        });
        break;
    }
    
    // 应用类别筛选（如有）
    if (this.selectedCategoryFilter !== undefined) {
      filteredList = filteredList.filter(item => item.itemType === this.selectedCategoryFilter);
    }

    // 应用位置筛选（仅匹配一级位置前缀，如 '客厅'）
    if (this.selectedLocationFilter && this.selectedLocationFilter.trim() !== '') {
      const prefix = this.selectedLocationFilter + '/';
      filteredList = filteredList.filter(item => item.location.startsWith(prefix) || item.location === this.selectedLocationFilter);
    }

    // 如果有搜索文本，再根据搜索文本过滤
    if (this.isSearching && this.searchText.trim() !== '') {
      const searchStr = this.searchText.toLowerCase().trim();
      filteredList = filteredList.filter(item => 
        item.name.toLowerCase().includes(searchStr) || 
        item.location.toLowerCase().includes(searchStr)
      );
    }
    
    return filteredList;
  }

  /**
   * 打开添加菜单
   */
  openAddMenu() {
    // 打开任何弹层前，收起搜索气泡
    if (this.showSearchBubbles) {
      this.showSearchBubbles = false;
    }
    // 先设置显示状态为true并设置初始透明度为0
    this.showAddMenu = true;
    this.menuOpacity = 0;
    this.menuTranslateY = -30; // 增大初始偏移量，使动画更明显
    
    // 定时器延迟执行动画，确保UI已经渲染
    setTimeout(() => {
      // 改变透明度和位置，使菜单滑入
      this.menuOpacity = 1;
      this.menuTranslateY = 0;
    }, 10);
  }

  /**
   * 关闭添加菜单
   */
  closeAddMenu() {
    // 设置动画回到隐藏状态
    this.menuOpacity = 0;
    this.menuTranslateY = -30; // 增大关闭时的偏移量，使动画更明显
    
    // 等待动画完成后再隐藏组件
    setTimeout(() => {
      this.showAddMenu = false;
    }, 250);
  }

  /**
   * 格式化位置
   */
  formatLocation(location: string[]): string {
    return location.join('/');
  }

  /**
   * 打开位置选择器
   */
  openLocationPicker() {
    // 打开位置选择器前，收起搜索气泡
    if (this.showSearchBubbles) {
      this.showSearchBubbles = false;
    }
    // 初始化临时位置
    this.tempLocation = [...this.selectedLocation];
    this.currentPickerLevel = 0;
    this.showCustomInput = false;
    
    // 确保第二级选项与第一级选择匹配
    this.updateSecondLevelOptions(this.tempLocation[0]);
    
    // 显示位置选择器
    this.showLocationPicker = true;
  }
  
  /**
   * 更新位置词条
   */
  updateLocationItem() {
    // 确保输入不为空
    if (this.editingLocationNewValue.trim() === '') {
      promptAction.showToast({ message: '词条名称不能为空' });
      return;
    }
    
    // 根据当前编辑的层级更新对应的词条
    if (this.editingLocationLevel === 0) {
      // 更新第一级位置（区域）
      // 1. 保存原有的第二级选项
      const secondLevelItems = this.locationMap[this.editingLocationItem] || [];
      
      // 2. 创建新的locationMap对象替换旧的
      const newLocationMap: Record<string, string[]> = {};
      Object.keys(this.locationMap).forEach(key => {
        if (key !== this.editingLocationItem) {
          newLocationMap[key] = this.locationMap[key];
        }
      });
      
      // 3. 添加新的键，值为原来的第二级选项
      newLocationMap[this.editingLocationNewValue] = secondLevelItems;
      
      // 4. 替换旧的locationMap
      this.locationMap = newLocationMap;
      
      // 5. 更新位置选择列表
      this.locationLevels[0] = Object.keys(this.locationMap);
      
      // 6. 如果当前选择的是被修改的项，也更新选择
      if (this.tempLocation[0] === this.editingLocationItem) {
        this.tempLocation[0] = this.editingLocationNewValue;
      }
    } else if (this.editingLocationLevel === 1) {
      // 更新第二级位置（家具）
      const areaKey = this.tempLocation[0];
      if (areaKey && this.locationMap[areaKey]) {
        // 找到当前词条在数组中的索引
        const index = this.locationMap[areaKey].indexOf(this.editingLocationItem);
        if (index !== -1) {
          // 替换该位置的词条
          this.locationMap[areaKey][index] = this.editingLocationNewValue;
          
          // 更新位置选择列表
          this.locationLevels[1] = this.locationMap[areaKey];
          
          // 如果当前选择的是被修改的项，也更新选择
          if (this.tempLocation[1] === this.editingLocationItem) {
            this.tempLocation[1] = this.editingLocationNewValue;
          }
        }
      }
    } else if (this.editingLocationLevel === 2) {
      // 更新第三级位置（具体位置）
      // 找到当前词条在数组中的索引
      const index = this.thirdLevelOptions.indexOf(this.editingLocationItem);
      if (index !== -1) {
        // 替换该位置的词条
        this.thirdLevelOptions[index] = this.editingLocationNewValue;
        
        // 更新位置选择列表
        this.locationLevels[2] = this.thirdLevelOptions;
        
        // 如果当前选择的是被修改的项，也更新选择
        if (this.tempLocation[2] === this.editingLocationItem) {
          this.tempLocation[2] = this.editingLocationNewValue;
        }
      }
    }
    
    // 关闭编辑对话框
    this.showLocationEditDialog = false;
    
    // 提示更新成功
    promptAction.showToast({ message: '词条更新成功' });
  }
  
  /**
   * 删除位置词条
   */
  deleteLocationItem() {
    // 根据当前编辑的层级删除对应的词条
    if (this.editingLocationLevel === 0) {
      // 删除第一级位置（区域）
      // 不能删除所有区域，至少保留一个
      if (Object.keys(this.locationMap).length <= 1) {
        promptAction.showToast({ message: '必须保留至少一个区域' });
        return;
      }
      
      // 创建新的locationMap替换旧的，排除要删除的项
      const newLocationMap: Record<string, string[]> = {};
      Object.keys(this.locationMap).forEach(key => {
        if (key !== this.editingLocationItem) {
          newLocationMap[key] = this.locationMap[key];
        }
      });
      
      // 更新locationMap
      this.locationMap = newLocationMap;
      
      // 更新位置选择列表
      this.locationLevels[0] = Object.keys(this.locationMap);
      
      // 如果当前选择的是被删除的项，重置为第一个可用选项
      if (this.tempLocation[0] === this.editingLocationItem) {
        this.tempLocation[0] = this.locationLevels[0][0];
        this.updateSecondLevelOptions(this.tempLocation[0]);
      }
    } else if (this.editingLocationLevel === 1) {
      // 删除第二级位置（家具）
      const areaKey = this.tempLocation[0];
      if (areaKey && this.locationMap[areaKey]) {
        // 不能删除所有家具选项，至少保留"其他"
        if (this.locationMap[areaKey].length <= 1) {
          promptAction.showToast({ message: '必须保留至少一个选项' });
          return;
        }
        
        // 找到当前词条在数组中的索引
        const index = this.locationMap[areaKey].indexOf(this.editingLocationItem);
        if (index !== -1) {
          // 从数组中删除该词条
          this.locationMap[areaKey].splice(index, 1);
          
          // 更新位置选择列表
          this.locationLevels[1] = this.locationMap[areaKey];
          
          // 如果当前选择的是被删除的项，重置为第一个可用选项
          if (this.tempLocation[1] === this.editingLocationItem) {
            this.tempLocation[1] = this.locationLevels[1][0];
          }
        }
      }
    } else if (this.editingLocationLevel === 2) {
      // 删除第三级位置（具体位置）
      // 不能删除所有选项，至少保留"其他"
      if (this.thirdLevelOptions.length <= 1) {
        promptAction.showToast({ message: '必须保留至少一个选项' });
        return;
      }
      
      // 找到当前词条在数组中的索引
      const index = this.thirdLevelOptions.indexOf(this.editingLocationItem);
      if (index !== -1) {
        // 从数组中删除该词条
        this.thirdLevelOptions.splice(index, 1);
        
        // 更新位置选择列表
        this.locationLevels[2] = this.thirdLevelOptions;
        
        // 如果当前选择的是被删除的项，重置为第一个可用选项
        if (this.tempLocation[2] === this.editingLocationItem) {
          this.tempLocation[2] = this.thirdLevelOptions[0];
        }
      }
    }
    
    // 关闭编辑对话框
    this.showLocationEditDialog = false;
    
    // 提示删除成功
    promptAction.showToast({ message: '词条删除成功' });
  }

  /**
   * 创建新物品
   */
  createNewItem() {
    // 创建一个空白物品
    this.selectedItemType = ItemType.GENERAL;
    this.selectedItem = new MedicineItem('', '', $r('app.media.add'), 0, this.selectedItemType, 20.00, "month");
    this.isAddingNewItem = true;
    
    // 初始化编辑字段
    this.initEditFields();
    
    // 直接进入编辑模式
    this.isEditMode = true;
    
    // 关闭添加菜单并打开详情对话框
    this.closeAddMenu();
    this.openDetailDialog();
  }

  /**
   * 打开类型选择器
   */
  openTypeSelector() {
    // 刷新类型列表
    this.itemTypes = getAllItemTypes();
    // 重置添加类型状态
    this.showAddTypeInput = false;
    this.newTypeName = '';
    // 显示类型选择器
    this.showTypeSelector = true;
  }

  /**
   * 计算保质期至日期
   */
  calculateExpiryDate(): void {
    try {
      console.info('计算保质期至日期 - 开始计算', this.editProductionDate);
      
      // 检查保质期限是否为空
      const isExpiryPeriodEmpty = this.expiryUnitIsMonth 
        ? !this.editExpiryMonths || this.editExpiryMonths.trim() === ''
        : !this.editExpiryDays || this.editExpiryDays.trim() === '';
      
      // 如果保质期限为空，则表示物品无保质期（长期有效）
      if (isExpiryPeriodEmpty) {
        this.expiryDate = '无保质期';
        this.dynamicExpiryDays = 30; // 设置为长期有效
        console.info('保质期限为空，物品为无保质期（长期有效）');
        return;
      }
      
      if (!this.editProductionDate || this.editProductionDate.split('/').length !== 3) {
        // 如果生产日期格式不正确，使用当前日期
        const today: Date = new Date();
        this.editProductionDate = `${today.getFullYear()}/${String(today.getMonth() + 1).padStart(2, '0')}/${String(today.getDate()).padStart(2, '0')}`;
        console.info('计算保质期至日期 - 使用默认日期', this.editProductionDate);
      }
      
      // 解析生产日期
      const dateParts: number[] = this.editProductionDate.split('/').map(Number);
      const year: number = dateParts[0] || new Date().getFullYear();
      const month: number = dateParts[1] || (new Date().getMonth() + 1);
      const day: number = dateParts[2] || new Date().getDate();
      const productionDate: Date = new Date(year, month - 1, day); // 月份从0开始
      
      // 创建新的日期对象用于计算过期日期
      const expiryDate: Date = new Date(productionDate);
      
      // 获取保质期数值，确保是有效数字
      const expiryMonths: number = Number(this.editExpiryMonths) || 0;
      const expiryDays: number = Number(this.editExpiryDays) || 0;
      
      if (this.expiryUnitIsMonth) {
        // 如果单位是月，增加月份
        expiryDate.setMonth(expiryDate.getMonth() + expiryMonths);
      } else {
        // 如果单位是天，增加天数
        expiryDate.setDate(expiryDate.getDate() + expiryDays);
      }
      
      // 格式化日期为 YYYY/MM/DD
      const expYear: number = expiryDate.getFullYear();
      const expMonth: string = String(expiryDate.getMonth() + 1).padStart(2, '0');
      const expDay: string = String(expiryDate.getDate()).padStart(2, '0');
      this.expiryDate = `${expYear}/${expMonth}/${expDay}`;
      
      console.info('计算保质期至日期 - 结果', this.expiryDate);
      
      // 计算剩余天数更新过期标签
      const today: Date = new Date();
      const diffTime: number = expiryDate.getTime() - today.getTime();
      const diffDays: number = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // 更新过期天数 - 使用实际剩余天数
      this.dynamicExpiryDays = diffDays;
      console.info('计算保质期至日期 - 动态过期天数', this.dynamicExpiryDays);
    } catch (error) {
      console.error('计算保质期至日期出错', error);
    }
  }
  
  /**
   * 根据保质期至日期反向计算保质期限
   */
  calculateExpiryPeriod(): void {
    try {
      console.info('反向计算保质期限 - 开始计算', this.expiryDate, this.editProductionDate);
      
      // 检查保质期至日期是否为空或为无保质期
      if (!this.expiryDate || this.expiryDate.trim() === '' || this.expiryDate === '无保质期') {
        // 如果保质期至日期为空或为无保质期，则保质期限也设为空
        this.editExpiryMonths = '';
        this.editExpiryDays = '';
        this.dynamicExpiryDays = 30; // 设置为长期有效
        console.info('物品为无保质期（长期有效）');
        return;
      }
      
      // 检查日期格式是否正确
      if (!this.editProductionDate || 
          this.expiryDate.split('/').length !== 3 || 
          this.editProductionDate.split('/').length !== 3) {
        console.error('日期格式不正确，无法计算');
        return;
      }
      
      // 解析生产日期
      const prodDateParts: number[] = this.editProductionDate.split('/').map(Number);
      const prodYear: number = prodDateParts[0];
      const prodMonth: number = prodDateParts[1];
      const prodDay: number = prodDateParts[2];
      const productionDate: Date = new Date(prodYear, prodMonth - 1, prodDay); // 月份从0开始
      
      // 解析保质期至日期
      const expDateParts: number[] = this.expiryDate.split('/').map(Number);
      const expYear: number = expDateParts[0];
      const expMonth: number = expDateParts[1];
      const expDay: number = expDateParts[2];
      const expiryDate: Date = new Date(expYear, expMonth - 1, expDay); // 月份从0开始
      
      // 计算两个日期之间的差值（毫秒）
      const diffTime: number = expiryDate.getTime() - productionDate.getTime();
      
      // 如果保质期至日期早于生产日期，提示错误
      if (diffTime < 0) {
        promptAction.showToast({ message: '保质期至日期不能早于生产日期' });
        // 重置为原来的计算结果
        this.calculateExpiryDate();
        return;
      }
      
      // 计算天数差
      const diffDays: number = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      // 计算月份差（近似值）
      const diffMonths: number = Math.round(diffDays / 30);
      
      // 更新保质期限
      this.editExpiryDays = String(diffDays);
      this.editExpiryMonths = String(diffMonths);
      
      console.info('反向计算保质期限 - 结果', '天数:', diffDays, '月数:', diffMonths);
      
      // 计算剩余天数更新过期标签
      const today: Date = new Date();
      const remainingTime: number = expiryDate.getTime() - today.getTime();
      const remainingDays: number = Math.ceil(remainingTime / (1000 * 60 * 60 * 24));
      
      // 更新过期天数 - 使用实际剩余天数而不是静态值
      this.dynamicExpiryDays = remainingDays;
      
      console.info('计算保质期至日期 - 动态过期天数', this.dynamicExpiryDays);
    } catch (error) {
      console.error('反向计算保质期限出错', error);
    }
  }

  /**
   * 初始化对话框 - 删除这个占位符函数，我们实际上不需要它
   */
  
  /**
   * 打开语音输入弹窗
   */
  openVoiceDialog() {
    // 重置语音输入相关状态
    this.voiceInputText = '';
    this.realtimeText = '';
    this.recognitionStatus = '';
    this.recognitionError = '';
    this.isRecording = false;
    this.isRecognizing = false;

    // 先设置显示状态为true
    this.showVoiceDialog = true;

    // 添加动画效果，从底部向上滑出
    animateTo({
      duration: 300,
      curve: Curve.EaseOut,
      iterations: 1,
      playMode: PlayMode.Normal
    }, () => {
      this.voiceDialogTranslateY = 0;
      this.voiceDialogOpacity = 1;
    });
  }

  /**
   * 关闭语音输入弹窗
   */
  closeVoiceDialog() {
    // 如果正在录音或识别，先停止
    if (this.isRecording || this.isRecognizing) {
      this.cleanupRecognition();
    }

    // 添加动画效果，向底部滑回
    animateTo({
      duration: 300,
      curve: Curve.EaseOut,
      iterations: 1,
      playMode: PlayMode.Normal
    }, () => {
      this.voiceDialogTranslateY = 430;
      this.voiceDialogOpacity = 0;
    });

    // 延迟隐藏弹窗，等待动画完成
    setTimeout(() => {
      this.showVoiceDialog = false;
      // 清理状态
      this.realtimeText = '';
      this.recognitionStatus = '';
      this.recognitionError = '';
    }, 300);
  }

  /**
   * 处理语音输入
   */
  handleVoiceInput() {
    // 如果正在录音，结束录音
    if (this.isRecording) {
      this.stopSpeechRecognition();
      return;
    }
    
    // 否则开始录音
    this.startSpeechRecognition();
  }

  /**
   * 开始录音（已废弃，使用startSpeechRecognition替代）
   */
  startRecording() {
    this.startSpeechRecognition();
  }

  /**
   * 停止录音（已废弃，使用stopSpeechRecognition替代）
   */
  stopRecording() {
    this.stopSpeechRecognition();
  }

  /**
   * 确认语音输入
   */
  confirmVoiceInput() {
    // 获取最终的识别文本（优先使用完整识别结果，其次使用实时文本）
    const finalText = this.voiceInputText.trim() || this.realtimeText.trim();

    if (finalText === '') {
      promptAction.showToast({ message: '请先录音或输入内容' });
      return;
    }

    // 如果正在录音，先停止录音
    if (this.isRecording) {
      this.stopSpeechRecognition();
      // 等待识别完成后再处理
      setTimeout(() => {
        this.processVoiceInput();
      }, 1000);
      return;
    }

    this.processVoiceInput();
  }

  /**
   * 处理语音输入结果
   */
  private processVoiceInput() {
    const finalText = this.voiceInputText.trim() || this.realtimeText.trim();

    if (finalText === '') {
      promptAction.showToast({ message: '没有识别到有效内容' });
      return;
    }

    // 解析语音输入，尝试提取物品信息
    const parsedInfo = this.parseVoiceInput(finalText);

    // 创建一个新物品
    this.selectedItemType = parsedInfo.type || ItemType.GENERAL;
    this.selectedItem = new MedicineItem(
      parsedInfo.name || finalText,
      parsedInfo.description || '',
      $r('app.media.add'),
      parsedInfo.quantity || 0,
      this.selectedItemType,
      parsedInfo.price || 0.00
    );
    this.isAddingNewItem = true;

    // 初始化编辑字段
    this.initEditFields();

    // 如果解析出了位置信息，设置位置
    if (parsedInfo.location) {
      this.editLocation = parsedInfo.location;
    }

    // 直接进入编辑模式
    this.isEditMode = true;

    // 关闭语音输入弹窗并打开详情对话框
    this.closeVoiceDialog();
    this.openDetailDialog();

    promptAction.showToast({ message: '语音输入已转换为物品信息' });
  }

  /**
   * 解析语音输入，提取物品信息
   */
  private parseVoiceInput(text: string): VoiceInputResult {
    const result: VoiceInputResult = {};

    // 简单的关键词匹配来解析语音输入
    // 匹配"添加"、"放"等动作词
    const actionMatch = text.match(/(添加|放|存放|收纳)(.+)/);
    if (actionMatch) {
      const content = actionMatch[2];

      // 匹配数量
      const quantityMatch = content.match(/(\d+)个?(.+)/);
      if (quantityMatch) {
        result.quantity = parseInt(quantityMatch[1]);
        result.name = quantityMatch[2].replace(/到|在|里/, '').trim();
      } else {
        result.name = content.replace(/到|在|里/, '').trim();
      }

      // 匹配位置信息
      const locationMatch = text.match(/(客厅|厨房|卧室|书房|浴室|阳台|玄关)(.+)/);
      if (locationMatch) {
        result.location = locationMatch[1] + locationMatch[2];
        // 从名称中移除位置信息
        result.name = result.name?.replace(result.location, '').trim();
      }

      // 根据物品名称推断类型
      if (result.name) {
        if (result.name.includes('药') || result.name.includes('胶囊') || result.name.includes('片')) {
          result.type = ItemType.MEDICINE;
        } else if (result.name.includes('食') || result.name.includes('奶') || result.name.includes('水果')) {
          result.type = ItemType.FOOD;
        } else if (result.name.includes('充电') || result.name.includes('电子') || result.name.includes('手机')) {
          result.type = ItemType.ELECTRONIC;
        } else if (result.name.includes('衣') || result.name.includes('鞋') || result.name.includes('帽')) {
          result.type = ItemType.CLOTHING;
        } else if (result.name.includes('书') || result.name.includes('笔') || result.name.includes('本')) {
          result.type = ItemType.STATIONERY;
        }
      }
    } else {
      // 如果没有匹配到动作词，直接使用整个文本作为物品名称
      result.name = text.trim();
    }

    return result;
  }

  /**
   * 处理转换按钮点击
   */
  handleConvertButton() {
    // 如果正在录音，先停止录音
    if (this.isRecording) {
      this.stopSpeechRecognition();
      return;
    }

    const inputText = this.voiceInputText.trim() || this.realtimeText.trim();

    // 如果有语音输入文本，进行智能转换处理
    if (inputText !== '') {
      try {
        // 智能文本处理和优化
        const optimizedText = this.optimizeRecognizedText(inputText);
        this.voiceInputText = optimizedText;

        // 清除实时文本
        this.realtimeText = '';
        this.recognitionStatus = '文本已优化';

        promptAction.showToast({ message: '文本转换完成' });

        // 延迟清除状态提示
        setTimeout(() => {
          this.recognitionStatus = '';
        }, 2000);

      } catch (error) {
        console.error('文本转换失败:', error);
        this.recognitionError = '文本转换失败';
        promptAction.showToast({ message: '文本转换失败，请重试' });
      }
    } else {
      promptAction.showToast({ message: '请先进行语音输入' });
    }
  }

  /**
   * 优化识别的文本
   */
  private optimizeRecognizedText(text: string): string {
    let optimized = text;

    // 基本的文本清理和格式化
    optimized = optimized.trim();

    // 移除多余的空格
    optimized = optimized.replace(/\s+/g, ' ');

    // 标点符号优化
    optimized = optimized.replace(/\s*，\s*/g, '，');
    optimized = optimized.replace(/\s*。\s*/g, '。');
    optimized = optimized.replace(/\s*！\s*/g, '！');
    optimized = optimized.replace(/\s*？\s*/g, '？');

    // 常见词汇纠正
    const corrections = new Map<string, string>([
      ['苹果手机', 'iPhone'],
      ['安卓手机', 'Android手机'],
      ['充电宝', '移动电源'],
      ['感冒药', '感冒药品'],
      ['维生素', '维生素补充剂']
    ]);

    corrections.forEach((correct: string, wrong: string) => {
      optimized = optimized.replace(new RegExp(wrong, 'g'), correct);
    });

    // 确保首字母大写（如果是英文）
    if (/^[a-z]/.test(optimized)) {
      optimized = optimized.charAt(0).toUpperCase() + optimized.slice(1);
    }

    return optimized;
  }

  /**
   * 初始化数据库并加载数据
   */
  private initDatabase() {
    // 初始化数据库
    this.dbManager.initDatabase(() => {
      console.info('数据库初始化成功');
      
      // 导入样本数据（如果数据库为空）
      this.dbManager.importSampleDataIfNeeded(() => {
        // 加载数据库中的物品数据
        this.loadItemsFromDB();
      });
    });
  }

  /**
   * 从数据库加载物品数据
   */
  private loadItemsFromDB() {
    // 从数据库获取所有物品
    this.dbManager.getAllItems((items: MedicineItem[]) => {
      // 更新物品列表
      if (items.length > 0) {
        this.medicineList = items;
        
        // 更新总件数和总资产
        this.totalCount = this.medicineList.length;
        this.calculateTotalAssets();
        
        // 检查过期物品并生成通知
        this.checkExpiryItems();
        
        console.info('从数据库加载物品数据成功，数量:', items.length);
      }
    });
  }

  /**
   * 添加新物品到数据库
   * @param item 新物品
   */
  private addItemToDB(item: MedicineItem) {
    // 添加物品到数据库
    this.dbManager.addItem(item, (id: number) => {
      console.info('添加物品到数据库成功，ID:', id);
      
      // 重新加载物品列表
      this.loadItemsFromDB();
    });
  }

  /**
   * 处理添加物品操作
   */
  handleAddItem() {
    if (this.editName.trim() === '') {
      promptAction.showToast({
        message: '物品名称不能为空',
        duration: 3000
      });
      return;
    }
    
    if (this.editLocation.trim() === '') {
      promptAction.showToast({
        message: '物品位置不能为空',
        duration: 3000
      });
      return;
    }
    
    const price = parseFloat(this.editPrice);
    if (isNaN(price) || price < 0) {
      promptAction.showToast({
        message: '价格格式不正确',
        duration: 3000
      });
      return;
    }
    
    // 创建新的物品对象
    const newItem = new MedicineItem(
      this.editName,
      this.editLocation,
      $r('app.media.add'),
      this.dynamicExpiryDays,
      this.selectedItemType,
      price
    );
    
    // 添加物品到数据库
    this.addItemToDB(newItem);
    
    // 清空输入并关闭对话框
    this.editName = '';
    this.editLocation = '';
    this.editPrice = '20.00';
    this.selectedItemType = ItemType.GENERAL;
    this.showDetailDialog = false;
    this.isAddingNewItem = false;
  }

  /**
   * 处理删除物品操作
   * @param index 物品索引
   */
  handleDeleteItem(index: number) {
    // 获取物品ID
    const item = this.medicineList[index];
    if (item && item.id) {
      // 从数据库中删除物品
      this.dbManager.deleteItem(item.id, (success: boolean) => {
        if (success) {
          // 从列表中移除物品
          this.medicineList.splice(index, 1);
          
          // 更新总件数和总资产
          this.totalCount = this.medicineList.length;
          this.calculateTotalAssets();
          
          // 通知视图更新
          this.medicineList = [...this.medicineList];
          
          promptAction.showToast({
            message: '物品已删除',
            duration: 2000
          });
        } else {
          promptAction.showToast({
            message: '删除物品失败',
            duration: 3000
          });
        }
      });
    } else {
      // 如果没有ID，只从列表中移除（这种情况在实际应用中应该避免）
      this.medicineList.splice(index, 1);
      this.totalCount = this.medicineList.length;
      this.calculateTotalAssets();
      this.medicineList = [...this.medicineList];
    }
  }

  /**
   * 搜索操作
   */
  handleSearch() {
    if (this.searchText.trim() === '') {
      // 搜索关键词为空，重新加载所有物品
      this.loadItemsFromDB();
      this.isSearching = false;
      return;
    }
    
    // 进入搜索状态
    this.isSearching = true;
    
    // 使用数据库搜索物品
    this.searchItems();
  }
  
  /**
   * 取消搜索
   */
  cancelSearch() {
    this.searchText = '';
    this.isSearching = false;
    
    // 重新加载所有物品
    this.loadItemsFromDB();
  }

  /**
   * 删除物品
   * @param item 要删除的物品
   */
  private deleteItem(item: MedicineItem) {
    if (item.id !== undefined) {
      // 从数据库中删除物品
      this.dbManager.deleteItem(item.id, (success: boolean) => {
        if (success) {
          // 从列表中移除物品
          const index = this.medicineList.findIndex(i => i.id === item.id);
          if (index !== -1) {
            this.medicineList.splice(index, 1);
            
            // 更新总件数和总资产
            this.updateTotalCount();
            this.calculateTotalAssets();
          }
          
          // 显示删除成功提示
          promptAction.showToast({
            message: '物品已删除',
            duration: 2000
          });
        } else {
          promptAction.showToast({
            message: '删除物品失败',
            duration: 3000
          });
        }
      });
    } else {
      promptAction.showToast({
        message: '物品ID不存在，无法删除',
        duration: 3000
      });
    }
  }

  /**
   * 检查过期物品并生成通知
   */
  private checkExpiryItems() {
    if (this.medicineList && this.medicineList.length > 0) {
      // 使用通知管理器检查过期物品并生成通知
      this.notificationManager.checkExpiryAndNotify(this.medicineList);
    }
  }
  
  /**
   * 验证并调整购买日期和生产日期的关系
   * 确保购买日期不早于生产日期
   */
  validatePurchaseAndProductionDates() {
    // 检查两个日期是否都已设置
    if (!this.editPurchaseDate || !this.editProductionDate) {
      return; // 如果任一日期未设置，不进行验证
    }
    
    try {
      // 解析购买日期
      const purchaseParts = this.editPurchaseDate.split('/').map(Number);
      if (purchaseParts.length !== 3) {
        console.warn('购买日期格式错误:', this.editPurchaseDate);
        return;
      }
      const purchaseDate = new Date(purchaseParts[0], purchaseParts[1] - 1, purchaseParts[2]);
      
      // 解析生产日期
      const productionParts = this.editProductionDate.split('/').map(Number);
      if (productionParts.length !== 3) {
        console.warn('生产日期格式错误:', this.editProductionDate);
        return;
      }
      const productionDate = new Date(productionParts[0], productionParts[1] - 1, productionParts[2]);
      
      // 检查购买日期是否早于生产日期
      if (purchaseDate < productionDate) {
        // 将购买日期调整为生产日期
        this.editPurchaseDate = this.editProductionDate;
        
        // 显示提示信息
        promptAction.showToast({ 
          message: '购买日期不能早于生产日期，已自动调整为生产日期' 
        });
        
        console.info('购买日期已自动调整:', this.editPurchaseDate);
      }
      
    } catch (error) {
      console.error('日期验证过程中发生错误:', error);
    }
  }

  /**
   * 格式化DatePickerDialog回调值为日期字符串
   * 修复月份处理错误：HarmonyOS DatePickerDialog的value.month是0-11，需要加1转换为标准月份1-12
   * @param value DatePickerDialog onAccept回调的参数
   * @returns 格式化后的日期字符串 "YYYY/MM/DD"
   */
  private formatDatePickerValue(value: DatePickerValue): string {
    // 确保所有值都存在且有效
    if (value.year === undefined || value.month === undefined || value.day === undefined) {
      console.error('DatePicker值不完整:', value);
      return '';
    }
    
    // 根据HarmonyOS文档，DatePickerDialog的value.month是0-11，需要加1转换为标准月份1-12
    const formattedDate = `${value.year}/${String(value.month + 1).padStart(2, '0')}/${String(value.day).padStart(2, '0')}`;
    
    console.info('DatePicker原始值:', `year=${value.year}, month=${value.month}, day=${value.day}`);
    console.info('格式化后的日期:', formattedDate);
    
    return formattedDate;
  }

  /**
   * 初始化语音识别器
   */
  private async initSpeechRecognizer() {
    try {
      // 检查麦克风权限
      const hasPermission = await this.checkMicrophonePermission();
      if (!hasPermission) {
        throw new Error('麦克风权限未授予');
      }

      // 初始化华为语音识别引擎
      await huaweiSpeechRecognizer.initSpeechEngine();

      this.isRecognitionInitialized = true;
      console.info('华为语音识别器初始化成功');
    } catch (error) {
      console.error('语音识别器初始化失败:', error);
      this.recognitionError = this.getErrorMessage(error);

      // 显示用户友好的错误提示
      promptAction.showToast({
        message: this.recognitionError,
        duration: 3000
      });

      throw new Error(error?.toString() || '语音识别器初始化失败');
    }
  }

  /**
   * 检查麦克风权限
   */
  private async checkMicrophonePermission(): Promise<boolean> {
    try {
      // 获取应用访问控制管理器
      const atManager = abilityAccessCtrl.createAtManager();

      // 获取应用信息
      const bundleInfo = await bundleManager.getBundleInfoForSelf(bundleManager.BundleFlag.GET_BUNDLE_INFO_DEFAULT);
      const tokenId = bundleInfo.appInfo.accessTokenId;

      // 检查麦克风权限状态
      const permission: Permissions = 'ohos.permission.MICROPHONE';
      const grantStatus = await atManager.checkAccessToken(tokenId, permission);

      if (grantStatus === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
        console.info('麦克风权限已授予');
        return true;
      } else {
        console.info('麦克风权限未授予，开始申请权限');
        // 申请权限
        return await this.requestMicrophonePermission();
      }
    } catch (error) {
      console.error('检查麦克风权限失败:', error);
      // 如果检查失败，尝试申请权限
      return await this.requestMicrophonePermission();
    }
  }

  /**
   * 申请麦克风权限
   */
  private async requestMicrophonePermission(): Promise<boolean> {
    try {
      const atManager = abilityAccessCtrl.createAtManager();

      // 申请权限
      const permissions: Permissions[] = ['ohos.permission.MICROPHONE'];
      const context = getContext(this) as common.UIAbilityContext;
      const requestResult = await atManager.requestPermissionsFromUser(context, permissions);

      if (requestResult.authResults[0] === abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
        console.info('麦克风权限申请成功');
        return true;
      } else {
        console.error('麦克风权限申请被拒绝');
        promptAction.showToast({
          message: '需要麦克风权限才能使用语音功能，请在设置中手动开启',
          duration: 3000
        });
        return false;
      }
    } catch (error) {
      console.error('申请麦克风权限失败:', error);
      promptAction.showToast({
        message: '权限申请失败，请在设置中手动开启麦克风权限',
        duration: 3000
      });
      return false;
    }
  }

  /**
   * 获取用户友好的错误信息
   */
  private getErrorMessage(error: Error | string): string {
    const errorStr = error?.toString() || '';

    if (errorStr.includes('权限')) {
      return '需要麦克风权限才能使用语音功能';
    } else if (errorStr.includes('网络') || errorStr.includes('network')) {
      return '网络连接异常，请检查网络设置';
    } else if (errorStr.includes('初始化')) {
      return '语音功能初始化失败，请重试';
    } else if (errorStr.includes('识别')) {
      return '语音识别服务暂时不可用';
    } else {
      return '语音功能出现异常，请重试';
    }
  }

  /**
   * 开始语音识别
   */
  private async startSpeechRecognition() {
    try {
      this.isRecording = true;
      this.isRecognizing = true;
      this.recordingTime = 0;
      this.recognitionError = '';
      this.voiceInputText = '';
      this.realtimeText = '';
      this.recognitionStatus = '正在录音，请说话...';

      // 启动计时器
      this.recordingTimer = setInterval(() => {
        this.recordingTime++;
        if (this.recordingTime >= 60) {
          this.stopSpeechRecognition();
        }
      }, 1000) as number;

      // 设置华为语音识别回调
      huaweiSpeechRecognizer.onRecognitionStart = () => {
        this.recognitionStatus = '开始识别...';
      };

      huaweiSpeechRecognizer.onRecognitionResult = (text: string) => {
        this.realtimeText = text;
        this.voiceInputText = text;
        this.recognitionStatus = '正在识别...';
      };

      huaweiSpeechRecognizer.onRecognitionComplete = () => {
        this.recognitionStatus = '识别完成';
        this.isRecording = false;
        this.isRecognizing = false;
        this.realtimeText = '';

        // 清除计时器
        if (this.recordingTimer !== -1) {
          clearInterval(this.recordingTimer);
          this.recordingTimer = -1;
        }

        promptAction.showToast({ message: '语音识别完成' });

        // 延迟清除状态提示
        setTimeout(() => {
          this.recognitionStatus = '';
        }, 2000);
      };

      huaweiSpeechRecognizer.onRecognitionError = (error: string) => {
        this.recognitionError = error;
        this.recognitionStatus = '识别失败';
        this.isRecording = false;
        this.isRecognizing = false;

        // 清除计时器
        if (this.recordingTimer !== -1) {
          clearInterval(this.recordingTimer);
          this.recordingTimer = -1;
        }

        promptAction.showToast({ message: error });
      };

      // 开始华为语音识别
      await huaweiSpeechRecognizer.startRecognition();

      promptAction.showToast({ message: '开始录音，请说话...' });

    } catch (error) {
      console.error('开始语音识别失败:', error);
      this.recognitionError = this.getErrorMessage(error);
      this.recognitionStatus = '启动失败';
      this.isRecording = false;
      this.isRecognizing = false;

      // 尝试重新初始化
      this.attemptRecovery();

      promptAction.showToast({
        message: this.recognitionError,
        duration: 3000
      });
    }
  }

  /**
   * 停止语音识别
   */
  private async stopSpeechRecognition() {
    // 检查是否正在录音或识别
    if (!this.isRecording && !this.isRecognizing) {
      console.warn('语音识别未在进行中');
      return;
    }

    try {
      console.info('开始停止语音识别');
      this.recognitionStatus = '正在处理语音...';

      // 清除计时器
      if (this.recordingTimer !== -1) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = -1;
      }
      this.recordingTime = 0;

      // 停止华为语音识别
      await huaweiSpeechRecognizer.stopRecognition();

      // 状态会在回调中更新，这里只显示处理中提示
      promptAction.showToast({ message: '正在处理语音...' });

    } catch (error) {
      console.error('停止语音识别失败:', error);
      this.recognitionError = this.getErrorMessage(error);
      this.recognitionStatus = '处理失败';
      this.isRecording = false;
      this.isRecognizing = false;

      // 清除计时器
      if (this.recordingTimer !== -1) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = -1;
      }
      this.recordingTime = 0;

      promptAction.showToast({ message: '语音处理失败，请重试' });
    }
  }

  /**
   * 尝试恢复语音识别功能
   */
  private async attemptRecovery() {
    try {
      console.info('尝试恢复语音识别功能...');

      // 清理当前状态
      this.cleanupRecognition();

      // 延迟重新初始化
      setTimeout(async () => {
        try {
          await this.initSpeechRecognizer();
          console.info('语音识别功能恢复成功');
        } catch (error) {
          console.error('语音识别功能恢复失败:', error);
        }
      }, 2000);

    } catch (error) {
      console.error('恢复语音识别功能失败:', error);
    }
  }

  /**
   * 清理语音识别相关资源
   */
  private cleanupRecognition() {
    try {
      // 清除计时器
      if (this.recordingTimer !== -1) {
        clearInterval(this.recordingTimer);
        this.recordingTimer = -1;
      }

      // 取消华为语音识别
      huaweiSpeechRecognizer.cancelRecognition().catch((error: Error) => {
        console.error('取消华为语音识别失败:', error);
      });

      // 重置状态
      this.isRecording = false;
      this.isRecognizing = false;
      this.recordingTime = 0;

      console.info('华为语音识别资源清理完成');
    } catch (error) {
      console.error('清理语音识别资源失败:', error);
    }
  }

  /**
   * 释放华为语音识别器资源
   */
  private async releaseSpeechRecognizer() {
    try {
      await huaweiSpeechRecognizer.release();
      console.info('华为语音识别器资源已释放');
    } catch (error) {
      console.error('释放华为语音识别器资源失败:', error);
    }
  }
}