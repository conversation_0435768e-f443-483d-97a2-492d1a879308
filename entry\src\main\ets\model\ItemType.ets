/**
 * 物品类型枚举
 */
export enum ItemType {
  GENERAL = 0,     // 普通物品
  MEDICINE = 1,    // 药品
  FOOD = 2,        // 食品
  VEGETABLE = 3,   // 蔬菜
  FRUIT = 4,       // 水果
  MEAT = 5,        // 肉类
  SEAFOOD = 6,     // 海鲜
  EGG = 7,         // 蛋类
  MILK = 8,        // 乳制品
  ELECTRONIC = 9,  // 电子产品
  CLOTHING = 10,   // 衣物
  BOOK = 11,       // 书籍
  TOY = 12,        // 玩具
  COSMETIC = 13,   // 化妆品
  CLEANING = 14,   // 清洁用品
  STATIONERY = 15, // 文具
  TOOL = 16,       // 工具
  DOCUMENT = 17,   // 文件
  JEWELRY = 18,    // 珠宝首饰
  LUGGAGE = 19,    // 行李/箱包
  SPORTS = 20,     // 体育用品
  DECORATION = 21, // 装饰品
  OTHER = 99,      // 其他
  CUSTOM = 100     // 自定义类型的基础值
}

/**
 * 自定义类型类
 */
export class CustomItemType {
  id: number;
  name: string;
  icon: ResourceStr;

  constructor(id: number, name: string, icon: ResourceStr = $r('app.media.add')) {
    this.id = id;
    this.name = name;
    this.icon = icon;
  }

  get type(): ItemType {
    return this.id as ItemType;
  }
}

/**
 * 获取物品类型对应的图标
 */
export function getItemTypeIcon(type: ItemType): ResourceStr {
  // 根据物品类型返回对应的彩色手绘风格图标
  switch (type) {
    case ItemType.GENERAL:
      return $r('app.media.icon_general_color');
    case ItemType.MEDICINE:
      return $r('app.media.icon_medicine_color');
    case ItemType.FOOD:
      return $r('app.media.icon_food_color');
    case ItemType.VEGETABLE:
      return $r('app.media.icon_vegetable_color');
    case ItemType.FRUIT:
      return $r('app.media.icon_fruit_color');
    case ItemType.MEAT:
      return $r('app.media.icon_meat_color');
    case ItemType.SEAFOOD:
      return $r('app.media.icon_seafood_color');
    case ItemType.EGG:
      return $r('app.media.icon_egg_color');
    case ItemType.MILK:
      return $r('app.media.icon_milk_color');
    case ItemType.ELECTRONIC:
      return $r('app.media.icon_electronic_color');
    case ItemType.CLOTHING:
      return $r('app.media.icon_clothing_color');
    case ItemType.BOOK:
      return $r('app.media.icon_book_color');
    case ItemType.TOY:
      return $r('app.media.icon_toy_color');
    case ItemType.COSMETIC:
      return $r('app.media.icon_cosmetic_color');
    case ItemType.CLEANING:
      return $r('app.media.icon_cleaning_color');
    case ItemType.STATIONERY:
      return $r('app.media.icon_stationery_color');
    case ItemType.TOOL:
      return $r('app.media.icon_tool_color');
    case ItemType.DOCUMENT:
      return $r('app.media.icon_document_color');
    case ItemType.JEWELRY:
      return $r('app.media.icon_jewelry_color');
    case ItemType.LUGGAGE:
      return $r('app.media.icon_luggage_color');
    case ItemType.SPORTS:
      return $r('app.media.icon_sports_color');
    case ItemType.DECORATION:
      return $r('app.media.icon_decoration_color');
    case ItemType.OTHER:
      return $r('app.media.icon_other_color');
    default:
      // 对于自定义类型，使用通用图标
      return $r('app.media.add');
  }
}

/**
 * 获取物品类型名称
 */
export function getItemTypeName(type: ItemType): string {
  switch (type) {
    case ItemType.GENERAL:
      return '普通物品';
    case ItemType.MEDICINE:
      return '药品';
    case ItemType.FOOD:
      return '食品';
    case ItemType.VEGETABLE:
      return '蔬菜';
    case ItemType.FRUIT:
      return '水果';
    case ItemType.MEAT:
      return '肉类';
    case ItemType.SEAFOOD:
      return '海鲜';
    case ItemType.EGG:
      return '蛋类';
    case ItemType.MILK:
      return '乳制品';
    case ItemType.ELECTRONIC:
      return '电子产品';
    case ItemType.CLOTHING:
      return '衣物';
    case ItemType.BOOK:
      return '书籍';
    case ItemType.TOY:
      return '玩具';
    case ItemType.COSMETIC:
      return '化妆品';
    case ItemType.CLEANING:
      return '清洁用品';
    case ItemType.STATIONERY:
      return '文具';
    case ItemType.TOOL:
      return '工具';
    case ItemType.DOCUMENT:
      return '文件';
    case ItemType.JEWELRY:
      return '珠宝首饰';
    case ItemType.LUGGAGE:
      return '行李/箱包';
    case ItemType.SPORTS:
      return '体育用品';
    case ItemType.DECORATION:
      return '装饰品';
    case ItemType.OTHER:
      return '其他';
    default:
      return '未知类型';
  }
}

// 自定义类型列表
export let CUSTOM_ITEM_TYPES: CustomItemType[] = [];

/**
 * 获取自定义类型
 */
export function getCustomType(typeId: ItemType): CustomItemType | undefined {
  return CUSTOM_ITEM_TYPES.find(item => item.id === typeId);
}

/**
 * 添加自定义类型
 */
export function addCustomType(name: string): CustomItemType {
  const id = ItemType.CUSTOM + CUSTOM_ITEM_TYPES.length;
  const customType = new CustomItemType(id, name);
  CUSTOM_ITEM_TYPES.push(customType);
  return customType;
}

/**
 * 所有物品类型数组
 */
export const ALL_ITEM_TYPES: ItemType[] = [
  ItemType.GENERAL,
  ItemType.MEDICINE,
  ItemType.FOOD,
  ItemType.VEGETABLE,
  ItemType.FRUIT,
  ItemType.MEAT,
  ItemType.SEAFOOD,
  ItemType.EGG,
  ItemType.MILK,
  ItemType.ELECTRONIC,
  ItemType.CLOTHING,
  ItemType.BOOK,
  ItemType.TOY,
  ItemType.COSMETIC,
  ItemType.CLEANING,
  ItemType.STATIONERY,
  ItemType.TOOL,
  ItemType.DOCUMENT,
  ItemType.JEWELRY,
  ItemType.LUGGAGE,
  ItemType.SPORTS,
  ItemType.DECORATION,
  ItemType.OTHER,
];

/**
 * 获取所有物品类型（包括自定义类型）
 */
export function getAllItemTypes(): ItemType[] {
  const allTypes: ItemType[] = [...ALL_ITEM_TYPES];
  CUSTOM_ITEM_TYPES.forEach(customType => {
    allTypes.push(customType.type);
  });
  return allTypes;
}